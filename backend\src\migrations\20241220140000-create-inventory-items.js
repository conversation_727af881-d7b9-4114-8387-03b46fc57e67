'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('inventory_items', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      item_code: {
        type: Sequelize.STRING(50),
        allowNull: true,
        unique: true
      },
      name: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      part_number: {
        type: Sequelize.STRING(100),
        allowNull: true,
        unique: true
      },
      brand: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      model: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      current_stock: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      minimum_stock: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      maximum_stock: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      reorder_level: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      reorder_quantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      unit_cost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      selling_price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      location: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      shelf: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      supplier: {
        type: Sequelize.STRING(200),
        allowNull: true
      },
      lead_time: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // إضافة فهارس
    await queryInterface.addIndex('inventory_items', ['item_code']);
    await queryInterface.addIndex('inventory_items', ['part_number']);
    await queryInterface.addIndex('inventory_items', ['name']);
    await queryInterface.addIndex('inventory_items', ['brand']);
    await queryInterface.addIndex('inventory_items', ['current_stock']);
    await queryInterface.addIndex('inventory_items', ['is_active']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('inventory_items');
  }
};

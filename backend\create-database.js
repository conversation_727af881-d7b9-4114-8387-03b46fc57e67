const { Sequelize } = require('sequelize');
require('dotenv').config();

async function createDatabase() {
  console.log('🔧 Creating database...');
  
  // الاتصال بقاعدة البيانات الافتراضية postgres لإنشاء قاعدة البيانات الجديدة
  const sequelize = new Sequelize(
    'postgres', // قاعدة البيانات الافتراضية
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: false // إخفاء SQL logs
    }
  );

  try {
    await sequelize.authenticate();
    console.log('✅ Connected to PostgreSQL server');
    
    // التحقق من وجود قاعدة البيانات
    const [results] = await sequelize.query(
      "SELECT 1 FROM pg_database WHERE datname = :dbname",
      {
        replacements: { dbname: process.env.DB_NAME },
        type: sequelize.QueryTypes.SELECT
      }
    );
    
    if (results) {
      console.log(`✅ Database '${process.env.DB_NAME}' already exists!`);
    } else {
      // إنشاء قاعدة البيانات
      await sequelize.query(`CREATE DATABASE "${process.env.DB_NAME}"`);
      console.log(`✅ Database '${process.env.DB_NAME}' created successfully!`);
    }
    
  } catch (error) {
    console.error('❌ Error creating database:', error.message);
    
    if (error.message.includes('already exists')) {
      console.log(`✅ Database '${process.env.DB_NAME}' already exists!`);
    } else {
      throw error;
    }
  } finally {
    await sequelize.close();
  }
}

// تشغيل الدالة
createDatabase()
  .then(() => {
    console.log('🎉 Database setup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Database setup failed:', error);
    process.exit(1);
  });

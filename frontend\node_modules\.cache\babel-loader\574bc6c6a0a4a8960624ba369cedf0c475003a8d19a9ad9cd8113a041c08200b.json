{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Fady\\\\frontend\\\\src\\\\components\\\\Equipment\\\\DeleteConfirmModal.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { deleteEquipment, selectEquipmentLoading } from '../../store/slices/equipmentSlice';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DeleteConfirmModal = ({\n  isOpen,\n  onClose,\n  equipment,\n  onSuccess\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const loading = useSelector(selectEquipmentLoading);\n  const handleDelete = async () => {\n    try {\n      await dispatch(deleteEquipment(equipment.id)).unwrap();\n      toast.success('تم حذف المعدة بنجاح');\n      onSuccess && onSuccess();\n      onClose();\n    } catch (error) {\n      toast.error(error || 'حدث خطأ أثناء حذف المعدة');\n    }\n  };\n  if (!isOpen || !equipment) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n          children: /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n            className: \"h-6 w-6 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-gray-900 mt-4\",\n          children: \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u062D\\u0630\\u0641 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 px-7 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: [\"\\u0647\\u0644 \\u0623\\u0646\\u062A \\u0645\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u062D\\u0630\\u0641 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0629 \\\"\", equipment.name, \"\\\"\\u061F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mt-2\",\n            children: [\"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0629: \", equipment.equipmentNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-red-500 mt-3\",\n            children: \"\\u0647\\u0630\\u0627 \\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621 \\u0644\\u0627 \\u064A\\u0645\\u0643\\u0646 \\u0627\\u0644\\u062A\\u0631\\u0627\\u062C\\u0639 \\u0639\\u0646\\u0647.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-3 space-x-reverse px-4 py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"btn-secondary\",\n            disabled: loading,\n            children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDelete,\n            className: \"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 disabled:opacity-50\",\n            disabled: loading,\n            children: loading ? 'جاري الحذف...' : 'حذف'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(DeleteConfirmModal, \"D2WQba4Mp0hvoLA9YhmXxxhKJnA=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DeleteConfirmModal;\nexport default DeleteConfirmModal;\nvar _c;\n$RefreshReg$(_c, \"DeleteConfirmModal\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "ExclamationTriangleIcon", "deleteEquipment", "selectEquipmentLoading", "toast", "jsxDEV", "_jsxDEV", "DeleteConfirmModal", "isOpen", "onClose", "equipment", "onSuccess", "_s", "dispatch", "loading", "handleDelete", "id", "unwrap", "success", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "equipmentNumber", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/components/Equipment/DeleteConfirmModal.js"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { deleteEquipment, selectEquipmentLoading } from '../../store/slices/equipmentSlice';\nimport toast from 'react-hot-toast';\n\nconst DeleteConfirmModal = ({ isOpen, onClose, equipment, onSuccess }) => {\n  const dispatch = useDispatch();\n  const loading = useSelector(selectEquipmentLoading);\n\n  const handleDelete = async () => {\n    try {\n      await dispatch(deleteEquipment(equipment.id)).unwrap();\n      toast.success('تم حذف المعدة بنجاح');\n      onSuccess && onSuccess();\n      onClose();\n    } catch (error) {\n      toast.error(error || 'حدث خطأ أثناء حذف المعدة');\n    }\n  };\n\n  if (!isOpen || !equipment) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3 text-center\">\n          <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100\">\n            <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600\" />\n          </div>\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mt-4\">\n            تأكيد حذف المعدة\n          </h3>\n          <div className=\"mt-2 px-7 py-3\">\n            <p className=\"text-sm text-gray-500\">\n              هل أنت متأكد من حذف المعدة \"{equipment.name}\"؟\n            </p>\n            <p className=\"text-sm text-gray-500 mt-2\">\n              رقم المعدة: {equipment.equipmentNumber}\n            </p>\n            <p className=\"text-xs text-red-500 mt-3\">\n              هذا الإجراء لا يمكن التراجع عنه.\n            </p>\n          </div>\n          <div className=\"flex justify-center space-x-3 space-x-reverse px-4 py-3\">\n            <button\n              onClick={onClose}\n              className=\"btn-secondary\"\n              disabled={loading}\n            >\n              إلغاء\n            </button>\n            <button\n              onClick={handleDelete}\n              className=\"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 disabled:opacity-50\"\n              disabled={loading}\n            >\n              {loading ? 'جاري الحذف...' : 'حذف'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DeleteConfirmModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,eAAe,EAAEC,sBAAsB,QAAQ,mCAAmC;AAC3F,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,OAAO,GAAGd,WAAW,CAACG,sBAAsB,CAAC;EAEnD,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMF,QAAQ,CAACX,eAAe,CAACQ,SAAS,CAACM,EAAE,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACtDb,KAAK,CAACc,OAAO,CAAC,qBAAqB,CAAC;MACpCP,SAAS,IAAIA,SAAS,CAAC,CAAC;MACxBF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdf,KAAK,CAACe,KAAK,CAACA,KAAK,IAAI,0BAA0B,CAAC;IAClD;EACF,CAAC;EAED,IAAI,CAACX,MAAM,IAAI,CAACE,SAAS,EAAE,OAAO,IAAI;EAEtC,oBACEJ,OAAA;IAAKc,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFf,OAAA;MAAKc,SAAS,EAAC,uEAAuE;MAAAC,QAAA,eACpFf,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bf,OAAA;UAAKc,SAAS,EAAC,4EAA4E;UAAAC,QAAA,eACzFf,OAAA,CAACL,uBAAuB;YAACmB,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNnB,OAAA;UAAIc,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnB,OAAA;UAAKc,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7Bf,OAAA;YAAGc,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,wIACP,EAACX,SAAS,CAACgB,IAAI,EAAC,UAC9C;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnB,OAAA;YAAGc,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GAAC,2DAC5B,EAACX,SAAS,CAACiB,eAAe;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACJnB,OAAA;YAAGc,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnB,OAAA;UAAKc,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEf,OAAA;YACEsB,OAAO,EAAEnB,OAAQ;YACjBW,SAAS,EAAC,eAAe;YACzBS,QAAQ,EAAEf,OAAQ;YAAAO,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnB,OAAA;YACEsB,OAAO,EAAEb,YAAa;YACtBK,SAAS,EAAC,qHAAqH;YAC/HS,QAAQ,EAAEf,OAAQ;YAAAO,QAAA,EAEjBP,OAAO,GAAG,eAAe,GAAG;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CA1DIL,kBAAkB;EAAA,QACLR,WAAW,EACZC,WAAW;AAAA;AAAA8B,EAAA,GAFvBvB,kBAAkB;AA4DxB,eAAeA,kBAAkB;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // إنشاء جدول أوامر الصيانة
      await queryInterface.createTable('work_orders', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        work_order_number: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true
        },
        customer_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'customers',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        equipment_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'equipment',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        // معلومات الاستلام
        received_date: {
          type: Sequelize.DATEONLY,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        received_by: {
          type: Sequelize.STRING(100),
          allowNull: false
        },
        // وصف المشكلة
        problem_description: {
          type: Sequelize.TEXT,
          allowNull: false
        },
        customer_complaint: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        // الفحص الأولي
        initial_diagnosis: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        estimated_cost: {
          type: Sequelize.DECIMAL(15, 2),
          allowNull: true
        },
        estimated_completion_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        // حالة أمر العمل
        status: {
          type: Sequelize.ENUM(
            'received',           // مستلم
            'diagnosed',          // تم التشخيص
            'approved',           // موافق عليه
            'in_progress',        // قيد التنفيذ
            'waiting_parts',      // انتظار قطع غيار
            'testing',            // اختبار
            'completed',          // مكتمل
            'delivered',          // مسلم
            'cancelled'           // ملغي
          ),
          defaultValue: 'received',
          allowNull: false
        },
        priority: {
          type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
          defaultValue: 'medium',
          allowNull: false
        },
        // التكاليف
        labor_cost: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        parts_cost: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        other_costs: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        total_cost: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        // التواريخ
        started_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        completed_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        delivered_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        // الفني المسؤول
        assigned_technician: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        // ملاحظات
        work_performed: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        internal_notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        customer_notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        // معلومات الضمان
        warranty_period: {
          type: Sequelize.INTEGER, // بالأيام
          allowNull: true
        },
        warranty_description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        // الصور والمرفقات
        before_images: {
          type: Sequelize.JSON,
          allowNull: true,
          defaultValue: []
        },
        after_images: {
          type: Sequelize.JSON,
          allowNull: true,
          defaultValue: []
        },
        documents: {
          type: Sequelize.JSON,
          allowNull: true,
          defaultValue: []
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // إنشاء جدول قطع الغيار المستخدمة
      await queryInterface.createTable('work_order_parts', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        work_order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'work_orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        part_name: {
          type: Sequelize.STRING(200),
          allowNull: false
        },
        part_number: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        quantity: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 1
        },
        unit_price: {
          type: Sequelize.DECIMAL(15, 2),
          allowNull: false
        },
        total_price: {
          type: Sequelize.DECIMAL(15, 2),
          allowNull: false
        },
        supplier: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // إنشاء جدول سجل العمل (تتبع التقدم)
      await queryInterface.createTable('work_logs', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        work_order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'work_orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        log_date: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        technician_name: {
          type: Sequelize.STRING(100),
          allowNull: false
        },
        hours_worked: {
          type: Sequelize.DECIMAL(5, 2),
          allowNull: false
        },
        work_description: {
          type: Sequelize.TEXT,
          allowNull: false
        },
        status_before: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        status_after: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // إضافة الفهارس
      await queryInterface.addIndex('work_orders', ['work_order_number'], { transaction });
      await queryInterface.addIndex('work_orders', ['customer_id'], { transaction });
      await queryInterface.addIndex('work_orders', ['equipment_id'], { transaction });
      await queryInterface.addIndex('work_orders', ['status'], { transaction });
      await queryInterface.addIndex('work_orders', ['received_date'], { transaction });
      await queryInterface.addIndex('work_orders', ['assigned_technician'], { transaction });

      await queryInterface.addIndex('work_order_parts', ['work_order_id'], { transaction });
      await queryInterface.addIndex('work_logs', ['work_order_id'], { transaction });
      await queryInterface.addIndex('work_logs', ['log_date'], { transaction });

      await transaction.commit();
      console.log('✅ Work Orders tables created successfully');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error creating work orders tables:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.dropTable('work_logs', { transaction });
      await queryInterface.dropTable('work_order_parts', { transaction });
      await queryInterface.dropTable('work_orders', { transaction });
      
      await transaction.commit();
      console.log('✅ Work Orders tables dropped successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error dropping work orders tables:', error);
      throw error;
    }
  }
};

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // إنشاء جدول العملاء
      await queryInterface.createTable('customers', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        customer_code: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true
        },
        name: {
          type: Sequelize.STRING(200),
          allowNull: false
        },
        type: {
          type: Sequelize.ENUM('individual', 'company'),
          defaultValue: 'company',
          allowNull: false
        },
        email: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        phone: {
          type: Sequelize.STRING(20),
          allowNull: true
        },
        mobile: {
          type: Sequelize.STRING(20),
          allowNull: true
        },
        tax_number: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        billing_address: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        billing_city: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        billing_country: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        contact_person_name: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        contact_person_phone: {
          type: Sequelize.STRING(20),
          allowNull: true
        },
        credit_limit: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        current_balance: {
          type: Sequelize.DECIMAL(15, 2),
          defaultValue: 0
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive', 'blocked'),
          defaultValue: 'active',
          allowNull: false
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // إنشاء جدول المعدات
      await queryInterface.createTable('equipment', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        equipment_number: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true
        },
        name: {
          type: Sequelize.STRING(100),
          allowNull: false
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        category: {
          type: Sequelize.ENUM('excavator', 'bulldozer', 'crane', 'loader', 'truck', 'generator', 'compressor', 'other'),
          allowNull: false
        },
        brand: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        model: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        serial_number: {
          type: Sequelize.STRING(100),
          allowNull: true,
          unique: true
        },
        year_of_manufacture: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        purchase_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        purchase_price: {
          type: Sequelize.DECIMAL(15, 2),
          allowNull: true
        },
        current_value: {
          type: Sequelize.DECIMAL(15, 2),
          allowNull: true
        },
        location: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        status: {
          type: Sequelize.ENUM('operational', 'maintenance', 'repair', 'out_of_service', 'sold'),
          defaultValue: 'operational',
          allowNull: false
        },
        condition: {
          type: Sequelize.ENUM('excellent', 'good', 'fair', 'poor'),
          defaultValue: 'good',
          allowNull: false
        },
        operating_hours: {
          type: Sequelize.INTEGER,
          defaultValue: 0
        },
        last_maintenance_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        next_maintenance_date: {
          type: Sequelize.DATEONLY,
          allowNull: true
        },
        maintenance_interval: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        images: {
          type: Sequelize.JSON,
          allowNull: true,
          defaultValue: []
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          defaultValue: true
        },
        customer_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'customers',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // إضافة الفهارس
      await queryInterface.addIndex('customers', ['customer_code'], { transaction });
      await queryInterface.addIndex('customers', ['name'], { transaction });
      await queryInterface.addIndex('customers', ['status'], { transaction });
      
      await queryInterface.addIndex('equipment', ['equipment_number'], { transaction });
      await queryInterface.addIndex('equipment', ['category'], { transaction });
      await queryInterface.addIndex('equipment', ['status'], { transaction });
      await queryInterface.addIndex('equipment', ['serial_number'], { transaction });
      await queryInterface.addIndex('equipment', ['customer_id'], { transaction });

      await transaction.commit();
      console.log('✅ Equipment tables created successfully');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error creating equipment tables:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.dropTable('equipment', { transaction });
      await queryInterface.dropTable('customers', { transaction });
      
      await transaction.commit();
      console.log('✅ Equipment tables dropped successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error dropping equipment tables:', error);
      throw error;
    }
  }
};

const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

async function testWorkOrderAPIs() {
  console.log('🔧 Testing Work Orders APIs...\n');

  try {
    // 1. تسجيل الدخول للحصول على التوكن
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!\n');

    const headers = {
      'Authorization': `Bearer ${token}`
    };

    // 2. اختبار إحصائيات أوامر الصيانة
    console.log('2️⃣ Testing Work Orders Stats...');
    const statsResponse = await axios.get(`${BASE_URL}/work-orders/stats`, { headers });
    console.log('✅ Work Orders Stats:');
    console.log('Total Work Orders:', statsResponse.data.data.totalWorkOrders);
    console.log('Overdue Work Orders:', statsResponse.data.data.overdueWorkOrders);
    console.log('Equipment in Workshop:', statsResponse.data.data.equipmentInWorkshop);
    console.log('Monthly Revenue:', statsResponse.data.data.monthlyRevenue);
    console.log('Status Stats:', statsResponse.data.data.statusStats);
    console.log('Priority Stats:', statsResponse.data.data.priorityStats);
    console.log('');

    // 3. اختبار الحصول على جميع أوامر الصيانة
    console.log('3️⃣ Testing Get All Work Orders...');
    const workOrdersResponse = await axios.get(`${BASE_URL}/work-orders?limit=5`, { headers });
    console.log('✅ Work Orders retrieved!');
    console.log('Total items:', workOrdersResponse.data.data.pagination.totalItems);
    console.log('Work Orders list:');
    workOrdersResponse.data.data.workOrders.forEach(wo => {
      console.log(`  - ${wo.workOrderNumber} - ${wo.status} - Customer: ${wo.customer?.name || 'None'} - Equipment: ${wo.equipment?.name || 'None'}`);
    });
    console.log('');

    // 4. اختبار البحث في أوامر الصيانة
    console.log('4️⃣ Testing Work Orders Search...');
    const searchResponse = await axios.get(`${BASE_URL}/work-orders?search=WO-2024`, { headers });
    console.log('✅ Search results:');
    searchResponse.data.data.workOrders.forEach(wo => {
      console.log(`  - ${wo.workOrderNumber} (${wo.problemDescription.substring(0, 50)}...)`);
    });
    console.log('');

    // 5. اختبار فلترة أوامر الصيانة حسب الحالة
    console.log('5️⃣ Testing Work Orders Filter by Status...');
    const filterResponse = await axios.get(`${BASE_URL}/work-orders?status=in_progress`, { headers });
    console.log('✅ In Progress Work Orders found:', filterResponse.data.data.pagination.totalItems);
    filterResponse.data.data.workOrders.forEach(wo => {
      console.log(`  - ${wo.workOrderNumber} (${wo.status})`);
    });
    console.log('');

    // 6. اختبار الحصول على أمر صيانة واحد مع التفاصيل
    console.log('6️⃣ Testing Get Work Order Details...');
    if (workOrdersResponse.data.data.workOrders.length > 0) {
      const firstWorkOrderId = workOrdersResponse.data.data.workOrders[0].id;
      const workOrderResponse = await axios.get(`${BASE_URL}/work-orders/${firstWorkOrderId}`, { headers });
      const workOrder = workOrderResponse.data.data.workOrder;
      console.log('✅ Work Order details:');
      console.log(`Number: ${workOrder.workOrderNumber}`);
      console.log(`Status: ${workOrder.status}`);
      console.log(`Customer: ${workOrder.customer?.name}`);
      console.log(`Equipment: ${workOrder.equipment?.name}`);
      console.log(`Problem: ${workOrder.problemDescription.substring(0, 100)}...`);
      console.log(`Parts count: ${workOrder.parts?.length || 0}`);
      console.log(`Work logs count: ${workOrder.workLogs?.length || 0}`);
      console.log('');
    }

    // 7. اختبار إنشاء أمر صيانة جديد
    console.log('7️⃣ Testing Create New Work Order...');
    
    // الحصول على عميل ومعدة للاختبار
    const customersResponse = await axios.get(`${BASE_URL}/customers?limit=1`, { headers });
    const equipmentResponse = await axios.get(`${BASE_URL}/equipment?limit=1`, { headers });
    
    if (customersResponse.data.data.customers.length > 0 && equipmentResponse.data.data.equipment.length > 0) {
      const newWorkOrder = {
        workOrderNumber: 'WO-TEST-001',
        customerId: customersResponse.data.data.customers[0].id,
        equipmentId: equipmentResponse.data.data.equipment[0].id,
        receivedBy: 'فني الاختبار',
        problemDescription: 'مشكلة اختبار في النظام',
        customerComplaint: 'العميل يشكو من مشكلة في المعدة',
        priority: 'medium',
        estimatedCost: 5000.00,
        assignedTechnician: 'فني الاختبار'
      };

      const createResponse = await axios.post(`${BASE_URL}/work-orders`, newWorkOrder, { headers });
      console.log('✅ Work Order created successfully!');
      console.log('New work order ID:', createResponse.data.data.workOrder.id);
      console.log('Work order number:', createResponse.data.data.workOrder.workOrderNumber);
      console.log('');

      // 8. اختبار تحديث أمر الصيانة
      console.log('8️⃣ Testing Update Work Order...');
      const workOrderId = createResponse.data.data.workOrder.id;
      const updateData = {
        status: 'diagnosed',
        initialDiagnosis: 'تم تشخيص المشكلة بنجاح',
        estimatedCost: 6000.00,
        internalNotes: 'تم تحديث أمر الصيانة للاختبار'
      };

      const updateResponse = await axios.put(`${BASE_URL}/work-orders/${workOrderId}`, updateData, { headers });
      console.log('✅ Work Order updated successfully!');
      console.log('Updated status:', updateResponse.data.data.workOrder.status);
      console.log('Initial diagnosis:', updateResponse.data.data.workOrder.initialDiagnosis);
      console.log('');

      // 9. اختبار حذف أمر الصيانة
      console.log('9️⃣ Testing Delete Work Order...');
      await axios.delete(`${BASE_URL}/work-orders/${workOrderId}`, { headers });
      console.log('✅ Work Order deleted successfully!');
      console.log('');
    } else {
      console.log('⚠️ No customers or equipment found for testing create/update/delete operations');
    }

    console.log('🎉 All Work Order API tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.errors) {
      console.error('Validation errors:', error.response.data.errors);
    }
  }
}

// تشغيل الاختبارات
testWorkOrderAPIs();

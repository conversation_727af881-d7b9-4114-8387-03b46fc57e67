import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { equipmentService, customerService } from '../../services/equipmentService';

// Async thunks for equipment
export const fetchEquipment = createAsyncThunk(
  'equipment/fetchEquipment',
  async (params, { rejectWithValue }) => {
    try {
      const response = await equipmentService.getAll(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch equipment');
    }
  }
);

export const fetchEquipmentById = createAsyncThunk(
  'equipment/fetchEquipmentById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await equipmentService.getById(id);
      return response.data.equipment;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch equipment');
    }
  }
);

export const createEquipment = createAsyncThunk(
  'equipment/createEquipment',
  async (equipmentData, { rejectWithValue }) => {
    try {
      const response = await equipmentService.create(equipmentData);
      return response.data.equipment;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create equipment');
    }
  }
);

export const updateEquipment = createAsyncThunk(
  'equipment/updateEquipment',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await equipmentService.update(id, data);
      return response.data.equipment;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update equipment');
    }
  }
);

export const deleteEquipment = createAsyncThunk(
  'equipment/deleteEquipment',
  async (id, { rejectWithValue }) => {
    try {
      await equipmentService.delete(id);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete equipment');
    }
  }
);

export const fetchEquipmentStats = createAsyncThunk(
  'equipment/fetchEquipmentStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await equipmentService.getStats();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch equipment stats');
    }
  }
);

// Async thunks for customers
export const fetchCustomers = createAsyncThunk(
  'equipment/fetchCustomers',
  async (params, { rejectWithValue }) => {
    try {
      const response = await customerService.getAll(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch customers');
    }
  }
);

export const fetchCustomersForSelect = createAsyncThunk(
  'equipment/fetchCustomersForSelect',
  async (search, { rejectWithValue }) => {
    try {
      const response = await customerService.getForSelect(search);
      return response.data.customers;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch customers');
    }
  }
);

const initialState = {
  // Equipment state
  equipment: [],
  selectedEquipment: null,
  equipmentStats: null,
  equipmentLoading: false,
  equipmentError: null,
  
  // Customers state
  customers: [],
  customersForSelect: [],
  customersLoading: false,
  customersError: null,
  
  // Pagination
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  },
  
  // Filters
  filters: {
    search: '',
    category: '',
    status: '',
    customer: '',
    sortBy: 'created_at',
    sortOrder: 'DESC'
  }
};

const equipmentSlice = createSlice({
  name: 'equipment',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    setSelectedEquipment: (state, action) => {
      state.selectedEquipment = action.payload;
    },
    clearSelectedEquipment: (state) => {
      state.selectedEquipment = null;
    },
    clearErrors: (state) => {
      state.equipmentError = null;
      state.customersError = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch equipment
    builder
      .addCase(fetchEquipment.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(fetchEquipment.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        state.equipment = action.payload.equipment;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchEquipment.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Fetch equipment by ID
    builder
      .addCase(fetchEquipmentById.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(fetchEquipmentById.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        state.selectedEquipment = action.payload;
      })
      .addCase(fetchEquipmentById.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Create equipment
    builder
      .addCase(createEquipment.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(createEquipment.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        state.equipment.unshift(action.payload);
      })
      .addCase(createEquipment.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Update equipment
    builder
      .addCase(updateEquipment.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(updateEquipment.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        const index = state.equipment.findIndex(eq => eq.id === action.payload.id);
        if (index !== -1) {
          state.equipment[index] = action.payload;
        }
        if (state.selectedEquipment?.id === action.payload.id) {
          state.selectedEquipment = action.payload;
        }
      })
      .addCase(updateEquipment.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Delete equipment
    builder
      .addCase(deleteEquipment.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(deleteEquipment.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        state.equipment = state.equipment.filter(eq => eq.id !== action.payload);
        if (state.selectedEquipment?.id === action.payload) {
          state.selectedEquipment = null;
        }
      })
      .addCase(deleteEquipment.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Fetch equipment stats
    builder
      .addCase(fetchEquipmentStats.pending, (state) => {
        state.equipmentLoading = true;
        state.equipmentError = null;
      })
      .addCase(fetchEquipmentStats.fulfilled, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentStats = action.payload;
      })
      .addCase(fetchEquipmentStats.rejected, (state, action) => {
        state.equipmentLoading = false;
        state.equipmentError = action.payload;
      });

    // Fetch customers
    builder
      .addCase(fetchCustomers.pending, (state) => {
        state.customersLoading = true;
        state.customersError = null;
      })
      .addCase(fetchCustomers.fulfilled, (state, action) => {
        state.customersLoading = false;
        state.customers = action.payload.customers;
      })
      .addCase(fetchCustomers.rejected, (state, action) => {
        state.customersLoading = false;
        state.customersError = action.payload;
      });

    // Fetch customers for select
    builder
      .addCase(fetchCustomersForSelect.pending, (state) => {
        state.customersLoading = true;
        state.customersError = null;
      })
      .addCase(fetchCustomersForSelect.fulfilled, (state, action) => {
        state.customersLoading = false;
        state.customersForSelect = action.payload;
      })
      .addCase(fetchCustomersForSelect.rejected, (state, action) => {
        state.customersLoading = false;
        state.customersError = action.payload;
      });
  }
});

export const {
  setFilters,
  clearFilters,
  setSelectedEquipment,
  clearSelectedEquipment,
  clearErrors
} = equipmentSlice.actions;

// Selectors
export const selectEquipment = (state) => state.equipment.equipment;
export const selectSelectedEquipment = (state) => state.equipment.selectedEquipment;
export const selectEquipmentStats = (state) => state.equipment.equipmentStats;
export const selectEquipmentLoading = (state) => state.equipment.equipmentLoading;
export const selectEquipmentError = (state) => state.equipment.equipmentError;

export const selectCustomers = (state) => state.equipment.customers;
export const selectCustomersForSelect = (state) => state.equipment.customersForSelect;
export const selectCustomersLoading = (state) => state.equipment.customersLoading;
export const selectCustomersError = (state) => state.equipment.customersError;

export const selectPagination = (state) => state.equipment.pagination;
export const selectFilters = (state) => state.equipment.filters;

export default equipmentSlice.reducer;

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkOrderPart = sequelize.define('WorkOrderPart', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    quantityUsed: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'quantity_used',
      validate: {
        min: 0.01,
        notEmpty: true
      }
    },
    unitCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'unit_cost',
      validate: {
        min: 0,
        notEmpty: true
      }
    },
    totalCost: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      field: 'total_cost',
      validate: {
        min: 0,
        notEmpty: true
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isReturned: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_returned'
    },
    returnedQuantity: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'returned_quantity',
      validate: {
        min: 0
      }
    },
    returnReason: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'return_reason'
    }
  }, {
    tableName: 'work_order_parts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['work_order_id']
      },
      {
        fields: ['item_id']
      }
    ]
  });

  // Instance methods
  WorkOrderPart.prototype.getNetQuantityUsed = function() {
    return parseFloat(this.quantityUsed) - parseFloat(this.returnedQuantity);
  };

  WorkOrderPart.prototype.getNetCost = function() {
    const netQuantity = this.getNetQuantityUsed();
    return netQuantity * parseFloat(this.unitCost);
  };

  // Associations
  WorkOrderPart.associate = function(models) {
    // WorkOrderPart belongs to WorkOrder
    if (models.WorkOrder) {
      WorkOrderPart.belongsTo(models.WorkOrder, {
        foreignKey: 'work_order_id',
        as: 'workOrder'
      });
    }

    // WorkOrderPart belongs to InventoryItem
    if (models.InventoryItem) {
      WorkOrderPart.belongsTo(models.InventoryItem, {
        foreignKey: 'item_id',
        as: 'item'
      });
    }
  };

  return WorkOrderPart;
};

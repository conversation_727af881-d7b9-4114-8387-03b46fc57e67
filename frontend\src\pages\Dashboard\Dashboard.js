import React from 'react';
import { useSelector } from 'react-redux';
import {
  CogIcon,
  ArchiveBoxIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { selectUser } from '../../store/slices/authSlice';

const Dashboard = () => {
  const user = useSelector(selectUser);

  // بيانات وهمية للإحصائيات
  const stats = [
    {
      name: 'إجمالي المعدات',
      value: '24',
      change: '+2',
      changeType: 'increase',
      icon: CogIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'أصناف المخزون',
      value: '156',
      change: '+12',
      changeType: 'increase',
      icon: ArchiveBoxIcon,
      color: 'bg-green-500',
    },
    {
      name: 'أوامر الصيانة النشطة',
      value: '8',
      change: '-3',
      changeType: 'decrease',
      icon: WrenchScrewdriverIcon,
      color: 'bg-yellow-500',
    },
    {
      name: 'الفواتير المعلقة',
      value: '5',
      change: '+1',
      changeType: 'increase',
      icon: DocumentTextIcon,
      color: 'bg-red-500',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'maintenance',
      title: 'تم إنشاء أمر صيانة جديد',
      description: 'صيانة دورية للحفارة CAT-001',
      time: 'منذ ساعتين',
      user: 'أحمد محمد',
    },
    {
      id: 2,
      type: 'inventory',
      title: 'تنبيه مخزون منخفض',
      description: 'فلاتر الزيت - الكمية المتبقية: 5 قطع',
      time: 'منذ 3 ساعات',
      user: 'النظام',
    },
    {
      id: 3,
      type: 'invoice',
      title: 'فاتورة جديدة',
      description: 'فاتورة صيانة للعميل شركة البناء المتطور',
      time: 'منذ 4 ساعات',
      user: 'سارة أحمد',
    },
    {
      id: 4,
      type: 'equipment',
      title: 'إضافة معدة جديدة',
      description: 'تم تسجيل رافعة شوكية جديدة FL-005',
      time: 'أمس',
      user: 'محمد علي',
    },
  ];

  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'صيانة مستحقة',
      message: 'الحفارة CAT-002 تحتاج صيانة دورية خلال 3 أيام',
      priority: 'متوسط',
    },
    {
      id: 2,
      type: 'error',
      title: 'مخزون منخفض',
      message: 'زيت المحرك - الكمية المتبقية: 2 لتر فقط',
      priority: 'عالي',
    },
    {
      id: 3,
      type: 'info',
      title: 'فاتورة مستحقة',
      message: 'فاتورة العميل ABC Company مستحقة خلال 5 أيام',
      priority: 'منخفض',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          مرحباً، {user?.firstName} {user?.lastName}
        </h1>
        <p className="text-indigo-100">
          مرحباً بك في نظام إدارة ورشة المعدات الثقيلة. إليك نظرة سريعة على أحدث الأنشطة.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <div className="flex items-center">
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                  <span className={`mr-2 text-sm ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h3>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 space-x-reverse">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <div className="h-2 w-2 bg-indigo-500 rounded-full"></div>
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                  <p className="text-sm text-gray-500">{activity.description}</p>
                  <div className="flex items-center mt-1 text-xs text-gray-400">
                    <span>{activity.time}</span>
                    <span className="mx-2">•</span>
                    <span>{activity.user}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Alerts */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">التنبيهات</h3>
          </div>
          <div className="space-y-4">
            {alerts.map((alert) => (
              <div key={alert.id} className={`p-4 rounded-lg border ${
                alert.type === 'error' ? 'bg-red-50 border-red-200' :
                alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                'bg-blue-50 border-blue-200'
              }`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className={`h-5 w-5 ${
                      alert.type === 'error' ? 'text-red-400' :
                      alert.type === 'warning' ? 'text-yellow-400' :
                      'text-blue-400'
                    }`} />
                  </div>
                  <div className="mr-3 flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{alert.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${
                      alert.priority === 'عالي' ? 'bg-red-100 text-red-800' :
                      alert.priority === 'متوسط' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      أولوية {alert.priority}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

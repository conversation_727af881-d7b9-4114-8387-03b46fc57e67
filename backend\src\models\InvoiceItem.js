const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InvoiceItem = sequelize.define('InvoiceItem', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    lineNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'line_number',
      validate: {
        min: 1
      }
    },
    description: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        len: [2, 500],
        notEmpty: true
      }
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0.01,
        notEmpty: true
      }
    },
    unitPrice: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      field: 'unit_price',
      validate: {
        min: 0,
        notEmpty: true
      }
    },
    discountRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      field: 'discount_rate',
      validate: {
        min: 0,
        max: 100
      }
    },
    discountAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'discount_amount',
      validate: {
        min: 0
      }
    },
    taxRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      field: 'tax_rate',
      validate: {
        min: 0,
        max: 100
      }
    },
    taxAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'tax_amount',
      validate: {
        min: 0
      }
    },
    lineTotal: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      field: 'line_total',
      validate: {
        min: 0,
        notEmpty: true
      }
    },
    unit: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [1, 20]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'invoice_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['invoice_id']
      },
      {
        fields: ['item_id']
      },
      {
        fields: ['line_number']
      }
    ]
  });

  // Instance methods
  InvoiceItem.prototype.calculateLineTotal = function() {
    const quantity = parseFloat(this.quantity);
    const unitPrice = parseFloat(this.unitPrice);
    const discountAmount = parseFloat(this.discountAmount);
    const taxAmount = parseFloat(this.taxAmount);
    
    const subtotal = quantity * unitPrice;
    const total = subtotal - discountAmount + taxAmount;
    
    return total.toFixed(2);
  };

  InvoiceItem.prototype.getSubtotal = function() {
    const quantity = parseFloat(this.quantity);
    const unitPrice = parseFloat(this.unitPrice);
    return (quantity * unitPrice).toFixed(2);
  };

  // Associations
  InvoiceItem.associate = function(models) {
    // InvoiceItem belongs to Invoice
    if (models.Invoice) {
      InvoiceItem.belongsTo(models.Invoice, {
        foreignKey: 'invoice_id',
        as: 'invoice'
      });
    }

    // InvoiceItem belongs to InventoryItem (optional, for inventory items)
    if (models.InventoryItem) {
      InvoiceItem.belongsTo(models.InventoryItem, {
        foreignKey: 'item_id',
        as: 'inventoryItem'
      });
    }
  };

  return InvoiceItem;
};

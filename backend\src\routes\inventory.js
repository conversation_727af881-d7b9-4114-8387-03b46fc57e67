const express = require('express');
const router = express.Router();
const {
  getAllInventoryItems,
  getInventoryItemById,
  createInventoryItem,
  updateInventoryItem,
  deleteInventoryItem,
  getInventoryStats,
  updateItemStock
} = require('../controllers/inventoryController');
const { authenticateToken } = require('../middleware/auth');

// تطبيق المصادقة على جميع المسارات
router.use(authenticateToken);

// مسارات عناصر المخزون
router.get('/items', getAllInventoryItems);
router.get('/items/:id', getInventoryItemById);
router.post('/items', createInventoryItem);
router.put('/items/:id', updateInventoryItem);
router.delete('/items/:id', deleteInventoryItem);

// مسارات إدارة المخزون
router.post('/items/:id/stock', updateItemStock);
router.get('/stats', getInventoryStats);

module.exports = router;

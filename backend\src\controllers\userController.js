const { User, Role, Permission } = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع المستخدمين
const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      role = '',
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (status) {
      whereClause.isActive = status === 'active';
    }

    // إعداد include للأدوار
    const includeOptions = [
      {
        model: Role,
        as: 'roles',
        attributes: ['id', 'name', 'displayName'],
        through: { attributes: [] }
      }
    ];

    // إضافة فلتر الدور إذا تم تحديده
    if (role) {
      includeOptions[0].where = { name: role };
      includeOptions[0].required = true;
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      include: includeOptions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy === 'createdAt' ? 'created_at' : sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على مستخدم واحد
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      include: [
        {
          model: Role,
          as: 'roles',
          include: [
            {
              model: Permission,
              as: 'permissions',
              attributes: ['id', 'name', 'displayName', 'module', 'action']
            }
          ],
          attributes: ['id', 'name', 'displayName', 'description'],
          through: { attributes: [] }
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء مستخدم جديد
const createUser = async (req, res) => {
  try {
    const { username, email, password, firstName, lastName, phone, roleIds = [] } = req.body;

    // التحقق من وجود المستخدم
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { email },
          { username }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: existingUser.email === email 
          ? 'Email already registered' 
          : 'Username already taken'
      });
    }

    // إنشاء المستخدم
    const user = await User.create({
      username,
      email,
      password,
      firstName,
      lastName,
      phone
    });

    // إضافة الأدوار
    if (roleIds.length > 0) {
      const roles = await Role.findAll({
        where: { id: roleIds }
      });
      await user.setRoles(roles);
    } else {
      // إضافة دور المستخدم العادي افتراضياً
      const userRole = await Role.findOne({ where: { name: 'user' } });
      if (userRole) {
        await user.addRole(userRole);
      }
    }

    // إعادة جلب المستخدم مع الأدوار
    const userWithRoles = await User.findByPk(user.id, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name', 'displayName'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: { user: userWithRoles }
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث مستخدم
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, phone, isActive, roleIds } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // تحديث البيانات الأساسية
    await user.update({
      firstName: firstName !== undefined ? firstName : user.firstName,
      lastName: lastName !== undefined ? lastName : user.lastName,
      phone: phone !== undefined ? phone : user.phone,
      isActive: isActive !== undefined ? isActive : user.isActive
    });

    // تحديث الأدوار إذا تم تمريرها
    if (roleIds !== undefined) {
      const roles = await Role.findAll({
        where: { id: roleIds }
      });
      await user.setRoles(roles);
    }

    // إعادة جلب المستخدم مع الأدوار
    const updatedUser = await User.findByPk(id, {
      include: [
        {
          model: Role,
          as: 'roles',
          attributes: ['id', 'name', 'displayName'],
          through: { attributes: [] }
        }
      ]
    });

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user: updatedUser }
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف مستخدم
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // التأكد من أن المستخدم لا يحذف نفسه
    if (id === req.userId) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete your own account'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    await user.destroy();

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تفعيل/إلغاء تفعيل مستخدم
const toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;

    // التأكد من أن المستخدم لا يلغي تفعيل نفسه
    if (id === req.userId) {
      return res.status(400).json({
        success: false,
        message: 'You cannot deactivate your own account'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    await user.update({ isActive: !user.isActive });

    res.json({
      success: true,
      message: `User ${user.isActive ? 'activated' : 'deactivated'} successfully`,
      data: { user }
    });

  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle user status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus
};

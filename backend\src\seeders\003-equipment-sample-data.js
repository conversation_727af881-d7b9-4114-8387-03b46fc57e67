const { v4: uuidv4 } = require('uuid');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      const now = new Date();

      // إنشاء عملاء تجريبيين
      const customers = [
        {
          id: uuidv4(),
          customer_code: 'CUST001',
          name: 'شركة البناء المتطور',
          type: 'company',
          email: '<EMAIL>',
          phone: '+************',
          mobile: '+************',
          tax_number: '*********',
          billing_address: 'شارع الملك فهد، الرياض',
          billing_city: 'الرياض',
          billing_country: 'المملكة العربية السعودية',
          contact_person_name: 'أحمد محمد',
          contact_person_phone: '+************',
          credit_limit: 500000.00,
          current_balance: 0.00,
          status: 'active',
          notes: 'عميل مميز - شركة إنشاءات كبيرة',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          customer_code: 'CUST002',
          name: 'مؤسسة الخليج للمقاولات',
          type: 'company',
          email: '<EMAIL>',
          phone: '+************',
          mobile: '+************',
          tax_number: '*********',
          billing_address: 'طريق الملك عبدالعزيز، جدة',
          billing_city: 'جدة',
          billing_country: 'المملكة العربية السعودية',
          contact_person_name: 'سارة أحمد',
          contact_person_phone: '+************',
          credit_limit: 300000.00,
          current_balance: 25000.00,
          status: 'active',
          notes: 'عميل منتظم - مقاولات متوسطة',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          customer_code: 'CUST003',
          name: 'محمد علي للتجارة',
          type: 'individual',
          email: '<EMAIL>',
          phone: '+************',
          mobile: '+************',
          billing_address: 'حي النخيل، الدمام',
          billing_city: 'الدمام',
          billing_country: 'المملكة العربية السعودية',
          contact_person_name: 'محمد علي',
          contact_person_phone: '+************',
          credit_limit: 100000.00,
          current_balance: 5000.00,
          status: 'active',
          notes: 'عميل فردي - مشاريع صغيرة',
          created_at: now,
          updated_at: now
        }
      ];

      await queryInterface.bulkInsert('customers', customers, { transaction });

      // إنشاء معدات تجريبية
      const equipment = [
        {
          id: uuidv4(),
          equipment_number: 'CAT001',
          name: 'حفارة كاتربيلر 320D',
          description: 'حفارة هيدروليكية متوسطة الحجم للأعمال العامة',
          category: 'excavator',
          brand: 'Caterpillar',
          model: '320D',
          serial_number: 'CAT320D2023001',
          year_of_manufacture: 2020,
          purchase_date: '2020-03-15',
          purchase_price: 450000.00,
          current_value: 380000.00,
          location: 'الرياض - المستودع الرئيسي',
          status: 'operational',
          condition: 'good',
          operating_hours: 2450,
          last_maintenance_date: '2024-01-15',
          next_maintenance_date: '2024-04-15',
          maintenance_interval: 250,
          notes: 'معدة في حالة جيدة، تحتاج صيانة دورية',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: customers[0].id,
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          equipment_number: 'KOM002',
          name: 'حفارة كوماتسو PC200',
          description: 'حفارة هيدروليكية يابانية عالية الجودة',
          category: 'excavator',
          brand: 'Komatsu',
          model: 'PC200-8',
          serial_number: 'KOM200PC2022001',
          year_of_manufacture: 2019,
          purchase_date: '2019-08-20',
          purchase_price: 420000.00,
          current_value: 340000.00,
          location: 'جدة - موقع المشروع',
          status: 'maintenance',
          condition: 'good',
          operating_hours: 3200,
          last_maintenance_date: '2024-01-20',
          next_maintenance_date: '2024-02-20',
          maintenance_interval: 200,
          notes: 'قيد الصيانة الدورية حالياً',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: customers[1].id,
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          equipment_number: 'CAT003',
          name: 'بلدوزر كاتربيلر D6T',
          description: 'بلدوزر متوسط للأعمال الترابية',
          category: 'bulldozer',
          brand: 'Caterpillar',
          model: 'D6T',
          serial_number: 'CATD6T2021001',
          year_of_manufacture: 2021,
          purchase_date: '2021-05-10',
          purchase_price: 520000.00,
          current_value: 460000.00,
          location: 'الدمام - المستودع',
          status: 'operational',
          condition: 'excellent',
          operating_hours: 1800,
          last_maintenance_date: '2024-01-10',
          next_maintenance_date: '2024-05-10',
          maintenance_interval: 300,
          notes: 'معدة جديدة نسبياً في حالة ممتازة',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: null,
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          equipment_number: 'LIE004',
          name: 'لودر ليبهير L556',
          description: 'لودر بعجل للتحميل والنقل',
          category: 'loader',
          brand: 'Liebherr',
          model: 'L556',
          serial_number: 'LIEL556-2020001',
          year_of_manufacture: 2020,
          purchase_date: '2020-11-25',
          purchase_price: 380000.00,
          current_value: 320000.00,
          location: 'الرياض - موقع العمل',
          status: 'operational',
          condition: 'good',
          operating_hours: 2100,
          last_maintenance_date: '2024-01-05',
          next_maintenance_date: '2024-03-05',
          maintenance_interval: 200,
          notes: 'يعمل بكفاءة عالية',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: customers[0].id,
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          equipment_number: 'GEN005',
          name: 'مولد كهربائي كاتربيلر',
          description: 'مولد كهربائي ديزل 500 كيلو وات',
          category: 'generator',
          brand: 'Caterpillar',
          model: 'C15-500KW',
          serial_number: 'CATGEN500-2022001',
          year_of_manufacture: 2022,
          purchase_date: '2022-02-14',
          purchase_price: 180000.00,
          current_value: 165000.00,
          location: 'جدة - المستودع',
          status: 'operational',
          condition: 'excellent',
          operating_hours: 850,
          last_maintenance_date: '2024-01-25',
          next_maintenance_date: '2024-04-25',
          maintenance_interval: 150,
          notes: 'مولد جديد، يعمل بكفاءة عالية',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: customers[2].id,
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          equipment_number: 'CRA006',
          name: 'رافعة برجية',
          description: 'رافعة برجية للمباني العالية',
          category: 'crane',
          brand: 'Potain',
          model: 'MDT219',
          serial_number: 'POT219-2018001',
          year_of_manufacture: 2018,
          purchase_date: '2018-09-30',
          purchase_price: 850000.00,
          current_value: 650000.00,
          location: 'الرياض - مشروع البرج',
          status: 'repair',
          condition: 'fair',
          operating_hours: 4500,
          last_maintenance_date: '2023-12-20',
          next_maintenance_date: '2024-02-01',
          maintenance_interval: 400,
          notes: 'تحتاج إصلاحات في النظام الهيدروليكي',
          images: JSON.stringify([]),
          is_active: true,
          customer_id: customers[0].id,
          created_at: now,
          updated_at: now
        }
      ];

      await queryInterface.bulkInsert('equipment', equipment, { transaction });

      await transaction.commit();
      console.log('✅ Equipment sample data seeded successfully');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error seeding equipment sample data:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.bulkDelete('equipment', null, { transaction });
      await queryInterface.bulkDelete('customers', null, { transaction });
      
      await transaction.commit();
      console.log('✅ Equipment sample data removed successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing equipment sample data:', error);
      throw error;
    }
  }
};

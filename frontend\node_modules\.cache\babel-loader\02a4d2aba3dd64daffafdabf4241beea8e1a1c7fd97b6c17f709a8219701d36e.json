{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Fady\\\\frontend\\\\src\\\\components\\\\UI\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  text = 'جاري التحميل...'\n}) => {\n  const sizeClasses = {\n    small: 'h-4 w-4',\n    medium: 'h-8 w-8',\n    large: 'h-12 w-12'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `animate-spin rounded-full border-b-2 border-indigo-600 ${sizeClasses[size]}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-4 text-sm text-gray-500\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "text", "sizeClasses", "small", "medium", "large", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/components/UI/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\n\nconst LoadingSpinner = ({ size = 'medium', text = 'جاري التحميل...' }) => {\n  const sizeClasses = {\n    small: 'h-4 w-4',\n    medium: 'h-8 w-8',\n    large: 'h-12 w-12'\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center py-12\">\n      <div className={`animate-spin rounded-full border-b-2 border-indigo-600 ${sizeClasses[size]}`}></div>\n      {text && (\n        <p className=\"mt-4 text-sm text-gray-500\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,IAAI,GAAG;AAAkB,CAAC,KAAK;EACxE,MAAMC,WAAW,GAAG;IAClBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,iDAAiD;IAAAC,QAAA,gBAC9DT,OAAA;MAAKQ,SAAS,EAAE,0DAA0DJ,WAAW,CAACF,IAAI,CAAC;IAAG;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACpGV,IAAI,iBACHH,OAAA;MAAGQ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAEN;IAAI;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CACpD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACC,EAAA,GAfIb,cAAc;AAiBpB,eAAeA,cAAc;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
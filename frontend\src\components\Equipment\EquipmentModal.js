import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { XMarkIcon } from '@heroicons/react/24/outline';
import {
  createEquipment,
  updateEquipment,
  fetchCustomersForSelect,
  selectCustomersForSelect,
  selectEquipmentLoading
} from '../../store/slices/equipmentSlice';
import {
  equipmentCategories,
  equipmentStatuses,
  equipmentConditions
} from '../../services/equipmentService';
import toast from 'react-hot-toast';

const EquipmentModal = ({ isOpen, onClose, equipment = null, onSuccess }) => {
  const dispatch = useDispatch();
  const customersForSelect = useSelector(selectCustomersForSelect);
  const loading = useSelector(selectEquipmentLoading);

  const [formData, setFormData] = useState({
    equipmentNumber: '',
    name: '',
    description: '',
    category: 'excavator',
    brand: '',
    model: '',
    serialNumber: '',
    yearOfManufacture: '',
    purchaseDate: '',
    purchasePrice: '',
    currentValue: '',
    location: '',
    status: 'operational',
    condition: 'good',
    operatingHours: 0,
    maintenanceInterval: '',
    notes: '',
    customerId: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isOpen) {
      dispatch(fetchCustomersForSelect());
      
      if (equipment) {
        // تعديل معدة موجودة
        setFormData({
          equipmentNumber: equipment.equipmentNumber || '',
          name: equipment.name || '',
          description: equipment.description || '',
          category: equipment.category || 'excavator',
          brand: equipment.brand || '',
          model: equipment.model || '',
          serialNumber: equipment.serialNumber || '',
          yearOfManufacture: equipment.yearOfManufacture || '',
          purchaseDate: equipment.purchaseDate || '',
          purchasePrice: equipment.purchasePrice || '',
          currentValue: equipment.currentValue || '',
          location: equipment.location || '',
          status: equipment.status || 'operational',
          condition: equipment.condition || 'good',
          operatingHours: equipment.operatingHours || 0,
          maintenanceInterval: equipment.maintenanceInterval || '',
          notes: equipment.notes || '',
          customerId: equipment.customerId || ''
        });
      } else {
        // إضافة معدة جديدة
        setFormData({
          equipmentNumber: '',
          name: '',
          description: '',
          category: 'excavator',
          brand: '',
          model: '',
          serialNumber: '',
          yearOfManufacture: '',
          purchaseDate: '',
          purchasePrice: '',
          currentValue: '',
          location: '',
          status: 'operational',
          condition: 'good',
          operatingHours: 0,
          maintenanceInterval: '',
          notes: '',
          customerId: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, equipment, dispatch]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // مسح الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.equipmentNumber.trim()) {
      newErrors.equipmentNumber = 'رقم المعدة مطلوب';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'اسم المعدة مطلوب';
    }

    if (!formData.category) {
      newErrors.category = 'فئة المعدة مطلوبة';
    }

    if (formData.yearOfManufacture && (formData.yearOfManufacture < 1900 || formData.yearOfManufacture > new Date().getFullYear())) {
      newErrors.yearOfManufacture = 'سنة الصنع غير صحيحة';
    }

    if (formData.purchasePrice && formData.purchasePrice < 0) {
      newErrors.purchasePrice = 'سعر الشراء لا يمكن أن يكون سالباً';
    }

    if (formData.currentValue && formData.currentValue < 0) {
      newErrors.currentValue = 'القيمة الحالية لا يمكن أن تكون سالبة';
    }

    if (formData.operatingHours < 0) {
      newErrors.operatingHours = 'ساعات التشغيل لا يمكن أن تكون سالبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        ...formData,
        yearOfManufacture: formData.yearOfManufacture ? parseInt(formData.yearOfManufacture) : null,
        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : null,
        currentValue: formData.currentValue ? parseFloat(formData.currentValue) : null,
        operatingHours: parseInt(formData.operatingHours) || 0,
        maintenanceInterval: formData.maintenanceInterval ? parseInt(formData.maintenanceInterval) : null,
        customerId: formData.customerId || null
      };

      if (equipment) {
        // تحديث معدة موجودة
        await dispatch(updateEquipment({ id: equipment.id, data: submitData })).unwrap();
        toast.success('تم تحديث المعدة بنجاح');
      } else {
        // إضافة معدة جديدة
        await dispatch(createEquipment(submitData)).unwrap();
        toast.success('تم إضافة المعدة بنجاح');
      }

      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      toast.error(error || 'حدث خطأ أثناء حفظ المعدة');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {equipment ? 'تعديل المعدة' : 'إضافة معدة جديدة'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* رقم المعدة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                رقم المعدة *
              </label>
              <input
                type="text"
                name="equipmentNumber"
                value={formData.equipmentNumber}
                onChange={handleChange}
                className={`input-field ${errors.equipmentNumber ? 'border-red-500' : ''}`}
                placeholder="مثال: CAT001"
              />
              {errors.equipmentNumber && (
                <p className="text-red-500 text-xs mt-1">{errors.equipmentNumber}</p>
              )}
            </div>

            {/* اسم المعدة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم المعدة *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`input-field ${errors.name ? 'border-red-500' : ''}`}
                placeholder="مثال: حفارة كاتربيلر 320D"
              />
              {errors.name && (
                <p className="text-red-500 text-xs mt-1">{errors.name}</p>
              )}
            </div>

            {/* الفئة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الفئة *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleChange}
                className={`input-field ${errors.category ? 'border-red-500' : ''}`}
              >
                {equipmentCategories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              {errors.category && (
                <p className="text-red-500 text-xs mt-1">{errors.category}</p>
              )}
            </div>

            {/* الماركة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الماركة
              </label>
              <input
                type="text"
                name="brand"
                value={formData.brand}
                onChange={handleChange}
                className="input-field"
                placeholder="مثال: Caterpillar"
              />
            </div>

            {/* الموديل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الموديل
              </label>
              <input
                type="text"
                name="model"
                value={formData.model}
                onChange={handleChange}
                className="input-field"
                placeholder="مثال: 320D"
              />
            </div>

            {/* الرقم التسلسلي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الرقم التسلسلي
              </label>
              <input
                type="text"
                name="serialNumber"
                value={formData.serialNumber}
                onChange={handleChange}
                className="input-field"
                placeholder="مثال: CAT320D2023001"
              />
            </div>

            {/* سنة الصنع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                سنة الصنع
              </label>
              <input
                type="number"
                name="yearOfManufacture"
                value={formData.yearOfManufacture}
                onChange={handleChange}
                className={`input-field ${errors.yearOfManufacture ? 'border-red-500' : ''}`}
                placeholder="مثال: 2020"
                min="1900"
                max={new Date().getFullYear()}
              />
              {errors.yearOfManufacture && (
                <p className="text-red-500 text-xs mt-1">{errors.yearOfManufacture}</p>
              )}
            </div>

            {/* تاريخ الشراء */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ الشراء
              </label>
              <input
                type="date"
                name="purchaseDate"
                value={formData.purchaseDate}
                onChange={handleChange}
                className="input-field"
              />
            </div>

            {/* سعر الشراء */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                سعر الشراء (ريال)
              </label>
              <input
                type="number"
                name="purchasePrice"
                value={formData.purchasePrice}
                onChange={handleChange}
                className={`input-field ${errors.purchasePrice ? 'border-red-500' : ''}`}
                placeholder="مثال: 450000"
                min="0"
                step="0.01"
              />
              {errors.purchasePrice && (
                <p className="text-red-500 text-xs mt-1">{errors.purchasePrice}</p>
              )}
            </div>

            {/* القيمة الحالية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                القيمة الحالية (ريال)
              </label>
              <input
                type="number"
                name="currentValue"
                value={formData.currentValue}
                onChange={handleChange}
                className={`input-field ${errors.currentValue ? 'border-red-500' : ''}`}
                placeholder="مثال: 380000"
                min="0"
                step="0.01"
              />
              {errors.currentValue && (
                <p className="text-red-500 text-xs mt-1">{errors.currentValue}</p>
              )}
            </div>

            {/* الموقع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الموقع
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleChange}
                className="input-field"
                placeholder="مثال: الرياض - المستودع الرئيسي"
              />
            </div>

            {/* الحالة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الحالة
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="input-field"
              >
                {equipmentStatuses.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>

            {/* الحالة العامة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الحالة العامة
              </label>
              <select
                name="condition"
                value={formData.condition}
                onChange={handleChange}
                className="input-field"
              >
                {equipmentConditions.map(condition => (
                  <option key={condition.value} value={condition.value}>
                    {condition.label}
                  </option>
                ))}
              </select>
            </div>

            {/* ساعات التشغيل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                ساعات التشغيل
              </label>
              <input
                type="number"
                name="operatingHours"
                value={formData.operatingHours}
                onChange={handleChange}
                className={`input-field ${errors.operatingHours ? 'border-red-500' : ''}`}
                placeholder="مثال: 2450"
                min="0"
              />
              {errors.operatingHours && (
                <p className="text-red-500 text-xs mt-1">{errors.operatingHours}</p>
              )}
            </div>

            {/* فترة الصيانة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                فترة الصيانة (ساعة)
              </label>
              <input
                type="number"
                name="maintenanceInterval"
                value={formData.maintenanceInterval}
                onChange={handleChange}
                className="input-field"
                placeholder="مثال: 250"
                min="1"
              />
            </div>

            {/* العميل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                العميل
              </label>
              <select
                name="customerId"
                value={formData.customerId}
                onChange={handleChange}
                className="input-field"
              >
                <option value="">اختر العميل</option>
                {customersForSelect.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.customerCode})
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* الوصف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الوصف
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="input-field"
              placeholder="وصف تفصيلي للمعدة..."
            />
          </div>

          {/* الملاحظات */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الملاحظات
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="input-field"
              placeholder="ملاحظات إضافية..."
            />
          </div>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex justify-end space-x-3 space-x-reverse pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? 'جاري الحفظ...' : (equipment ? 'تحديث' : 'إضافة')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EquipmentModal;

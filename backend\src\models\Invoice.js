const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Invoice = sequelize.define('Invoice', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    invoiceNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'invoice_number',
      validate: {
        len: [5, 50],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM,
      values: ['sale', 'purchase', 'service'],
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    status: {
      type: DataTypes.ENUM,
      values: ['draft', 'pending', 'sent', 'paid', 'overdue', 'cancelled'],
      defaultValue: 'draft',
      allowNull: false
    },
    issueDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'issue_date',
      defaultValue: DataTypes.NOW
    },
    dueDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'due_date'
    },
    paidDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'paid_date'
    },
    subtotal: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    taxRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      field: 'tax_rate',
      validate: {
        min: 0,
        max: 100
      }
    },
    taxAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'tax_amount',
      validate: {
        min: 0
      }
    },
    discountRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0,
      field: 'discount_rate',
      validate: {
        min: 0,
        max: 100
      }
    },
    discountAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'discount_amount',
      validate: {
        min: 0
      }
    },
    totalAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'total_amount',
      validate: {
        min: 0
      }
    },
    paidAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'paid_amount',
      validate: {
        min: 0
      }
    },
    balanceAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'balance_amount',
      validate: {
        min: 0
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    exchangeRate: {
      type: DataTypes.DECIMAL(10, 4),
      defaultValue: 1.0000,
      field: 'exchange_rate',
      validate: {
        min: 0.0001
      }
    },
    paymentTerms: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'payment_terms',
      validate: {
        len: [2, 100]
      }
    },
    paymentMethod: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'payment_method',
      validate: {
        len: [2, 50],
        isIn: [['cash', 'check', 'bank_transfer', 'credit_card', 'other']]
      }
    },
    referenceNumber: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'reference_number',
      validate: {
        len: [2, 100]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    internalNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'internal_notes'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    }
  }, {
    tableName: 'invoices',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['invoice_number']
      },
      {
        fields: ['customer_id']
      },
      {
        fields: ['supplier_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['issue_date']
      },
      {
        fields: ['due_date']
      }
    ]
  });

  // Instance methods
  Invoice.prototype.isOverdue = function() {
    if (!this.dueDate || this.status === 'paid' || this.status === 'cancelled') {
      return false;
    }
    return new Date() > new Date(this.dueDate);
  };

  Invoice.prototype.getDaysOverdue = function() {
    if (!this.isOverdue()) return 0;
    const today = new Date();
    const dueDate = new Date(this.dueDate);
    const diffTime = today - dueDate;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  Invoice.prototype.calculateTotals = function() {
    const subtotal = parseFloat(this.subtotal);
    const discountAmount = parseFloat(this.discountAmount);
    const taxAmount = parseFloat(this.taxAmount);
    
    const totalAmount = subtotal - discountAmount + taxAmount;
    const balanceAmount = totalAmount - parseFloat(this.paidAmount);
    
    return {
      totalAmount: totalAmount.toFixed(2),
      balanceAmount: balanceAmount.toFixed(2)
    };
  };

  Invoice.prototype.isPaid = function() {
    return this.status === 'paid' || parseFloat(this.balanceAmount) <= 0;
  };

  // Associations
  Invoice.associate = function(models) {
    // Invoice belongs to Customer (for sales invoices)
    Invoice.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer'
    });

    // Invoice belongs to Supplier (for purchase invoices)
    Invoice.belongsTo(models.Supplier, {
      foreignKey: 'supplier_id',
      as: 'supplier'
    });

    // Invoice belongs to User (created by)
    Invoice.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // Invoice has many InvoiceItems
    Invoice.hasMany(models.InvoiceItem, {
      foreignKey: 'invoice_id',
      as: 'items'
    });

    // Invoice has many Payments
    Invoice.hasMany(models.Payment, {
      foreignKey: 'invoice_id',
      as: 'payments'
    });

    // Invoice belongs to WorkOrder (for service invoices)
    Invoice.belongsTo(models.WorkOrder, {
      foreignKey: 'work_order_id',
      as: 'workOrder'
    });
  };

  return Invoice;
};

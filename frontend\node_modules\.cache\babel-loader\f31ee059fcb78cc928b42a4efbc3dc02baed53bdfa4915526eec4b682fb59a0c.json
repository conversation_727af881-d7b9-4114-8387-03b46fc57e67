{"ast": null, "code": "import api from './api';\n\n// Equipment API calls\nexport const equipmentService = {\n  // Get all equipment with filters and pagination\n  getAll: async (params = {}) => {\n    const response = await api.get('/equipment', {\n      params\n    });\n    return response.data;\n  },\n  // Get equipment by ID\n  getById: async id => {\n    const response = await api.get(`/equipment/${id}`);\n    return response.data;\n  },\n  // Create new equipment\n  create: async equipmentData => {\n    const response = await api.post('/equipment', equipmentData);\n    return response.data;\n  },\n  // Update equipment\n  update: async (id, equipmentData) => {\n    const response = await api.put(`/equipment/${id}`, equipmentData);\n    return response.data;\n  },\n  // Delete equipment\n  delete: async id => {\n    const response = await api.delete(`/equipment/${id}`);\n    return response.data;\n  },\n  // Get equipment statistics\n  getStats: async () => {\n    const response = await api.get('/equipment/stats');\n    return response.data;\n  }\n};\n\n// Customer API calls\nexport const customerService = {\n  // Get all customers with filters and pagination\n  getAll: async (params = {}) => {\n    const response = await api.get('/customers', {\n      params\n    });\n    return response.data;\n  },\n  // Get customers for select dropdown\n  getForSelect: async (search = '') => {\n    const response = await api.get('/customers/select', {\n      params: {\n        search\n      }\n    });\n    return response.data;\n  },\n  // Get customer by ID\n  getById: async id => {\n    const response = await api.get(`/customers/${id}`);\n    return response.data;\n  },\n  // Create new customer\n  create: async customerData => {\n    const response = await api.post('/customers', customerData);\n    return response.data;\n  },\n  // Update customer\n  update: async (id, customerData) => {\n    const response = await api.put(`/customers/${id}`, customerData);\n    return response.data;\n  },\n  // Delete customer\n  delete: async id => {\n    const response = await api.delete(`/customers/${id}`);\n    return response.data;\n  }\n};\n\n// Equipment categories and statuses\nexport const equipmentCategories = [{\n  value: 'excavator',\n  label: 'حفارة',\n  icon: '🚜'\n}, {\n  value: 'bulldozer',\n  label: 'بلدوزر',\n  icon: '🚛'\n}, {\n  value: 'crane',\n  label: 'رافعة',\n  icon: '🏗️'\n}, {\n  value: 'loader',\n  label: 'لودر',\n  icon: '🚚'\n}, {\n  value: 'truck',\n  label: 'شاحنة',\n  icon: '🚚'\n}, {\n  value: 'generator',\n  label: 'مولد كهربائي',\n  icon: '⚡'\n}, {\n  value: 'compressor',\n  label: 'ضاغط هواء',\n  icon: '💨'\n}, {\n  value: 'other',\n  label: 'أخرى',\n  icon: '🔧'\n}];\nexport const equipmentStatuses = [{\n  value: 'operational',\n  label: 'تشغيلية',\n  color: 'green'\n}, {\n  value: 'maintenance',\n  label: 'صيانة',\n  color: 'yellow'\n}, {\n  value: 'repair',\n  label: 'إصلاح',\n  color: 'red'\n}, {\n  value: 'out_of_service',\n  label: 'خارج الخدمة',\n  color: 'gray'\n}, {\n  value: 'sold',\n  label: 'مباعة',\n  color: 'blue'\n}];\nexport const equipmentConditions = [{\n  value: 'excellent',\n  label: 'ممتازة',\n  color: 'green'\n}, {\n  value: 'good',\n  label: 'جيدة',\n  color: 'blue'\n}, {\n  value: 'fair',\n  label: 'مقبولة',\n  color: 'yellow'\n}, {\n  value: 'poor',\n  label: 'سيئة',\n  color: 'red'\n}];\n\n// Helper functions\nexport const getEquipmentCategoryLabel = category => {\n  const cat = equipmentCategories.find(c => c.value === category);\n  return cat ? cat.label : category;\n};\nexport const getEquipmentStatusLabel = status => {\n  const stat = equipmentStatuses.find(s => s.value === status);\n  return stat ? stat.label : status;\n};\nexport const getEquipmentConditionLabel = condition => {\n  const cond = equipmentConditions.find(c => c.value === condition);\n  return cond ? cond.label : condition;\n};\nexport const getStatusColor = status => {\n  const stat = equipmentStatuses.find(s => s.value === status);\n  return stat ? stat.color : 'gray';\n};\nexport const getConditionColor = condition => {\n  const cond = equipmentConditions.find(c => c.value === condition);\n  return cond ? cond.color : 'gray';\n};\n\n// Format currency\nexport const formatCurrency = (amount, currency = 'SAR') => {\n  if (!amount) return '0';\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  }).format(amount);\n};\n\n// Format operating hours\nexport const formatOperatingHours = hours => {\n  if (!hours) return '0 ساعة';\n  return `${hours.toLocaleString('ar-SA')} ساعة`;\n};", "map": {"version": 3, "names": ["api", "equipmentService", "getAll", "params", "response", "get", "data", "getById", "id", "create", "equipmentData", "post", "update", "put", "delete", "getStats", "customerService", "getForSelect", "search", "customerData", "equipmentCategories", "value", "label", "icon", "equipmentStatuses", "color", "equipmentConditions", "getEquipmentCategoryLabel", "category", "cat", "find", "c", "getEquipmentStatusLabel", "status", "stat", "s", "getEquipmentConditionLabel", "condition", "cond", "getStatusColor", "getConditionColor", "formatCurrency", "amount", "currency", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "format", "formatOperatingHours", "hours", "toLocaleString"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/services/equipmentService.js"], "sourcesContent": ["import api from './api';\n\n// Equipment API calls\nexport const equipmentService = {\n  // Get all equipment with filters and pagination\n  getAll: async (params = {}) => {\n    const response = await api.get('/equipment', { params });\n    return response.data;\n  },\n\n  // Get equipment by ID\n  getById: async (id) => {\n    const response = await api.get(`/equipment/${id}`);\n    return response.data;\n  },\n\n  // Create new equipment\n  create: async (equipmentData) => {\n    const response = await api.post('/equipment', equipmentData);\n    return response.data;\n  },\n\n  // Update equipment\n  update: async (id, equipmentData) => {\n    const response = await api.put(`/equipment/${id}`, equipmentData);\n    return response.data;\n  },\n\n  // Delete equipment\n  delete: async (id) => {\n    const response = await api.delete(`/equipment/${id}`);\n    return response.data;\n  },\n\n  // Get equipment statistics\n  getStats: async () => {\n    const response = await api.get('/equipment/stats');\n    return response.data;\n  }\n};\n\n// Customer API calls\nexport const customerService = {\n  // Get all customers with filters and pagination\n  getAll: async (params = {}) => {\n    const response = await api.get('/customers', { params });\n    return response.data;\n  },\n\n  // Get customers for select dropdown\n  getForSelect: async (search = '') => {\n    const response = await api.get('/customers/select', { \n      params: { search } \n    });\n    return response.data;\n  },\n\n  // Get customer by ID\n  getById: async (id) => {\n    const response = await api.get(`/customers/${id}`);\n    return response.data;\n  },\n\n  // Create new customer\n  create: async (customerData) => {\n    const response = await api.post('/customers', customerData);\n    return response.data;\n  },\n\n  // Update customer\n  update: async (id, customerData) => {\n    const response = await api.put(`/customers/${id}`, customerData);\n    return response.data;\n  },\n\n  // Delete customer\n  delete: async (id) => {\n    const response = await api.delete(`/customers/${id}`);\n    return response.data;\n  }\n};\n\n// Equipment categories and statuses\nexport const equipmentCategories = [\n  { value: 'excavator', label: 'حفارة', icon: '🚜' },\n  { value: 'bulldozer', label: 'بلدوزر', icon: '🚛' },\n  { value: 'crane', label: 'رافعة', icon: '🏗️' },\n  { value: 'loader', label: 'لودر', icon: '🚚' },\n  { value: 'truck', label: 'شاحنة', icon: '🚚' },\n  { value: 'generator', label: 'مولد كهربائي', icon: '⚡' },\n  { value: 'compressor', label: 'ضاغط هواء', icon: '💨' },\n  { value: 'other', label: 'أخرى', icon: '🔧' }\n];\n\nexport const equipmentStatuses = [\n  { value: 'operational', label: 'تشغيلية', color: 'green' },\n  { value: 'maintenance', label: 'صيانة', color: 'yellow' },\n  { value: 'repair', label: 'إصلاح', color: 'red' },\n  { value: 'out_of_service', label: 'خارج الخدمة', color: 'gray' },\n  { value: 'sold', label: 'مباعة', color: 'blue' }\n];\n\nexport const equipmentConditions = [\n  { value: 'excellent', label: 'ممتازة', color: 'green' },\n  { value: 'good', label: 'جيدة', color: 'blue' },\n  { value: 'fair', label: 'مقبولة', color: 'yellow' },\n  { value: 'poor', label: 'سيئة', color: 'red' }\n];\n\n// Helper functions\nexport const getEquipmentCategoryLabel = (category) => {\n  const cat = equipmentCategories.find(c => c.value === category);\n  return cat ? cat.label : category;\n};\n\nexport const getEquipmentStatusLabel = (status) => {\n  const stat = equipmentStatuses.find(s => s.value === status);\n  return stat ? stat.label : status;\n};\n\nexport const getEquipmentConditionLabel = (condition) => {\n  const cond = equipmentConditions.find(c => c.value === condition);\n  return cond ? cond.label : condition;\n};\n\nexport const getStatusColor = (status) => {\n  const stat = equipmentStatuses.find(s => s.value === status);\n  return stat ? stat.color : 'gray';\n};\n\nexport const getConditionColor = (condition) => {\n  const cond = equipmentConditions.find(c => c.value === condition);\n  return cond ? cond.color : 'gray';\n};\n\n// Format currency\nexport const formatCurrency = (amount, currency = 'SAR') => {\n  if (!amount) return '0';\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0\n  }).format(amount);\n};\n\n// Format operating hours\nexport const formatOperatingHours = (hours) => {\n  if (!hours) return '0 ساعة';\n  return `${hours.toLocaleString('ar-SA')} ساعة`;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9B;EACAC,MAAM,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7B,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,YAAY,EAAE;MAAEF;IAAO,CAAC,CAAC;IACxD,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrB,MAAMJ,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,cAAcG,EAAE,EAAE,CAAC;IAClD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,MAAM,EAAE,MAAOC,aAAa,IAAK;IAC/B,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACW,IAAI,CAAC,YAAY,EAAED,aAAa,CAAC;IAC5D,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,MAAM,EAAE,MAAAA,CAAOJ,EAAE,EAAEE,aAAa,KAAK;IACnC,MAAMN,QAAQ,GAAG,MAAMJ,GAAG,CAACa,GAAG,CAAC,cAAcL,EAAE,EAAE,EAAEE,aAAa,CAAC;IACjE,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAQ,MAAM,EAAE,MAAON,EAAE,IAAK;IACpB,MAAMJ,QAAQ,GAAG,MAAMJ,GAAG,CAACc,MAAM,CAAC,cAAcN,EAAE,EAAE,CAAC;IACrD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,MAAMX,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,eAAe,GAAG;EAC7B;EACAd,MAAM,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7B,MAAMC,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,YAAY,EAAE;MAAEF;IAAO,CAAC,CAAC;IACxD,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAW,YAAY,EAAE,MAAAA,CAAOC,MAAM,GAAG,EAAE,KAAK;IACnC,MAAMd,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,mBAAmB,EAAE;MAClDF,MAAM,EAAE;QAAEe;MAAO;IACnB,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrB,MAAMJ,QAAQ,GAAG,MAAMJ,GAAG,CAACK,GAAG,CAAC,cAAcG,EAAE,EAAE,CAAC;IAClD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,MAAM,EAAE,MAAOU,YAAY,IAAK;IAC9B,MAAMf,QAAQ,GAAG,MAAMJ,GAAG,CAACW,IAAI,CAAC,YAAY,EAAEQ,YAAY,CAAC;IAC3D,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,MAAM,EAAE,MAAAA,CAAOJ,EAAE,EAAEW,YAAY,KAAK;IAClC,MAAMf,QAAQ,GAAG,MAAMJ,GAAG,CAACa,GAAG,CAAC,cAAcL,EAAE,EAAE,EAAEW,YAAY,CAAC;IAChE,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAQ,MAAM,EAAE,MAAON,EAAE,IAAK;IACpB,MAAMJ,QAAQ,GAAG,MAAMJ,GAAG,CAACc,MAAM,CAAC,cAAcN,EAAE,EAAE,CAAC;IACrD,OAAOJ,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,mBAAmB,GAAG,CACjC;EAAEC,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAK,CAAC,EAClD;EAAEF,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAK,CAAC,EACnD;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAM,CAAC,EAC/C;EAAEF,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,MAAM;EAAEC,IAAI,EAAE;AAAK,CAAC,EAC9C;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAK,CAAC,EAC9C;EAAEF,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE;AAAI,CAAC,EACxD;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAK,CAAC,EACvD;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,MAAM;EAAEC,IAAI,EAAE;AAAK,CAAC,CAC9C;AAED,OAAO,MAAMC,iBAAiB,GAAG,CAC/B;EAAEH,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,SAAS;EAAEG,KAAK,EAAE;AAAQ,CAAC,EAC1D;EAAEJ,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,OAAO;EAAEG,KAAK,EAAE;AAAS,CAAC,EACzD;EAAEJ,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,OAAO;EAAEG,KAAK,EAAE;AAAM,CAAC,EACjD;EAAEJ,KAAK,EAAE,gBAAgB;EAAEC,KAAK,EAAE,aAAa;EAAEG,KAAK,EAAE;AAAO,CAAC,EAChE;EAAEJ,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE,OAAO;EAAEG,KAAK,EAAE;AAAO,CAAC,CACjD;AAED,OAAO,MAAMC,mBAAmB,GAAG,CACjC;EAAEL,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,QAAQ;EAAEG,KAAK,EAAE;AAAQ,CAAC,EACvD;EAAEJ,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEG,KAAK,EAAE;AAAO,CAAC,EAC/C;EAAEJ,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE,QAAQ;EAAEG,KAAK,EAAE;AAAS,CAAC,EACnD;EAAEJ,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE,MAAM;EAAEG,KAAK,EAAE;AAAM,CAAC,CAC/C;;AAED;AACA,OAAO,MAAME,yBAAyB,GAAIC,QAAQ,IAAK;EACrD,MAAMC,GAAG,GAAGT,mBAAmB,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,KAAK,KAAKO,QAAQ,CAAC;EAC/D,OAAOC,GAAG,GAAGA,GAAG,CAACP,KAAK,GAAGM,QAAQ;AACnC,CAAC;AAED,OAAO,MAAMI,uBAAuB,GAAIC,MAAM,IAAK;EACjD,MAAMC,IAAI,GAAGV,iBAAiB,CAACM,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACd,KAAK,KAAKY,MAAM,CAAC;EAC5D,OAAOC,IAAI,GAAGA,IAAI,CAACZ,KAAK,GAAGW,MAAM;AACnC,CAAC;AAED,OAAO,MAAMG,0BAA0B,GAAIC,SAAS,IAAK;EACvD,MAAMC,IAAI,GAAGZ,mBAAmB,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,KAAK,KAAKgB,SAAS,CAAC;EACjE,OAAOC,IAAI,GAAGA,IAAI,CAAChB,KAAK,GAAGe,SAAS;AACtC,CAAC;AAED,OAAO,MAAME,cAAc,GAAIN,MAAM,IAAK;EACxC,MAAMC,IAAI,GAAGV,iBAAiB,CAACM,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACd,KAAK,KAAKY,MAAM,CAAC;EAC5D,OAAOC,IAAI,GAAGA,IAAI,CAACT,KAAK,GAAG,MAAM;AACnC,CAAC;AAED,OAAO,MAAMe,iBAAiB,GAAIH,SAAS,IAAK;EAC9C,MAAMC,IAAI,GAAGZ,mBAAmB,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,KAAK,KAAKgB,SAAS,CAAC;EACjE,OAAOC,IAAI,GAAGA,IAAI,CAACb,KAAK,GAAG,MAAM;AACnC,CAAC;;AAED;AACA,OAAO,MAAMgB,cAAc,GAAGA,CAACC,MAAM,EAAEC,QAAQ,GAAG,KAAK,KAAK;EAC1D,IAAI,CAACD,MAAM,EAAE,OAAO,GAAG;EACvB,OAAO,IAAIE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCC,KAAK,EAAE,UAAU;IACjBH,QAAQ,EAAEA,QAAQ;IAClBI,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;AACnB,CAAC;;AAED;AACA,OAAO,MAAMQ,oBAAoB,GAAIC,KAAK,IAAK;EAC7C,IAAI,CAACA,KAAK,EAAE,OAAO,QAAQ;EAC3B,OAAO,GAAGA,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,OAAO;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
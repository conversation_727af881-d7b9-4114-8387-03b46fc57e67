const { Sequelize } = require('sequelize');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 Testing database connection...');
  console.log(`📍 Host: ${process.env.DB_HOST}`);
  console.log(`🔌 Port: ${process.env.DB_PORT}`);
  console.log(`🗄️  Database: ${process.env.DB_NAME}`);
  console.log(`👤 User: ${process.env.DB_USER}`);
  console.log(`🔐 Password: ${process.env.DB_PASSWORD ? '***' : 'NOT SET'}`);

  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: console.log
    }
  );

  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully!');
    
    // اختبار إنشاء قاعدة البيانات إذا لم تكن موجودة
    const [results] = await sequelize.query(
      "SELECT 1 FROM pg_database WHERE datname = :dbname",
      {
        replacements: { dbname: process.env.DB_NAME },
        type: sequelize.QueryTypes.SELECT
      }
    );
    
    if (results) {
      console.log('✅ Database exists!');
    } else {
      console.log('⚠️  Database does not exist, but connection is working');
    }
    
  } catch (error) {
    console.error('❌ Unable to connect to the database:');
    console.error('Error name:', error.name);
    console.error('Error message:', error.message);
    
    if (error.name === 'SequelizeConnectionRefusedError') {
      console.log('\n🔧 Possible solutions:');
      console.log('1. Make sure PostgreSQL is running');
      console.log('2. Check if the port is correct (currently using:', process.env.DB_PORT, ')');
      console.log('3. Verify the host is accessible');
    } else if (error.name === 'SequelizeAccessDeniedError') {
      console.log('\n🔧 Possible solutions:');
      console.log('1. Check username and password');
      console.log('2. Make sure the user has access to the database');
      console.log('3. Try connecting with different credentials');
    } else if (error.name === 'SequelizeDatabaseError') {
      console.log('\n🔧 Possible solutions:');
      console.log('1. The database might not exist - create it first');
      console.log('2. Check database name spelling');
    }
  } finally {
    await sequelize.close();
  }
}

// تشغيل الاختبار
testConnection();

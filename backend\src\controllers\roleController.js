const { Role, Permission, User } = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع الأدوار
const getAllRoles = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      includePermissions = 'false',
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { displayName: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // إعداد include للصلاحيات
    const includeOptions = [];
    
    if (includePermissions === 'true') {
      includeOptions.push({
        model: Permission,
        as: 'permissions',
        attributes: ['id', 'name', 'displayName', 'module', 'action'],
        through: { attributes: [] }
      });
    }

    const { count, rows: roles } = await Role.findAndCountAll({
      where: whereClause,
      include: includeOptions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy === 'createdAt' ? 'created_at' : sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        roles,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all roles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على دور واحد
const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;

    const role = await Role.findByPk(id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'displayName', 'module', 'action', 'description'],
          through: { attributes: [] }
        },
        {
          model: User,
          as: 'users',
          attributes: ['id', 'username', 'firstName', 'lastName', 'email'],
          through: { attributes: [] }
        }
      ]
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.json({
      success: true,
      data: { role }
    });

  } catch (error) {
    console.error('Get role by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء دور جديد
const createRole = async (req, res) => {
  try {
    const { name, displayName, description, permissionIds = [] } = req.body;

    // التحقق من وجود الدور
    const existingRole = await Role.findOne({
      where: { name }
    });

    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: 'Role name already exists'
      });
    }

    // إنشاء الدور
    const role = await Role.create({
      name,
      displayName,
      description
    });

    // إضافة الصلاحيات
    if (permissionIds.length > 0) {
      const permissions = await Permission.findAll({
        where: { id: permissionIds }
      });
      await role.setPermissions(permissions);
    }

    // إعادة جلب الدور مع الصلاحيات
    const roleWithPermissions = await Role.findByPk(role.id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'displayName', 'module', 'action'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: { role: roleWithPermissions }
    });

  } catch (error) {
    console.error('Create role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث دور
const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, displayName, description, permissionIds } = req.body;

    const role = await Role.findByPk(id);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // التحقق من أن الدور ليس دور نظام
    if (role.isSystem) {
      return res.status(400).json({
        success: false,
        message: 'System roles cannot be modified'
      });
    }

    // التحقق من عدم تكرار اسم الدور
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({
        where: { 
          name,
          id: { [Op.ne]: id }
        }
      });

      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: 'Role name already exists'
        });
      }
    }

    // تحديث البيانات الأساسية
    await role.update({
      name: name || role.name,
      displayName: displayName || role.displayName,
      description: description !== undefined ? description : role.description
    });

    // تحديث الصلاحيات إذا تم تمريرها
    if (permissionIds !== undefined) {
      const permissions = await Permission.findAll({
        where: { id: permissionIds }
      });
      await role.setPermissions(permissions);
    }

    // إعادة جلب الدور مع الصلاحيات
    const updatedRole = await Role.findByPk(id, {
      include: [
        {
          model: Permission,
          as: 'permissions',
          attributes: ['id', 'name', 'displayName', 'module', 'action'],
          through: { attributes: [] }
        }
      ]
    });

    res.json({
      success: true,
      message: 'Role updated successfully',
      data: { role: updatedRole }
    });

  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف دور
const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    const role = await Role.findByPk(id, {
      include: [
        {
          model: User,
          as: 'users',
          attributes: ['id']
        }
      ]
    });

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // التحقق من أن الدور ليس دور نظام
    if (role.isSystem) {
      return res.status(400).json({
        success: false,
        message: 'System roles cannot be deleted'
      });
    }

    // التحقق من عدم وجود مستخدمين مرتبطين بالدور
    if (role.users && role.users.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete role that is assigned to users'
      });
    }

    await role.destroy();

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });

  } catch (error) {
    console.error('Delete role error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على جميع الصلاحيات
const getAllPermissions = async (req, res) => {
  try {
    const { module = '', groupByModule = 'false' } = req.query;

    const whereClause = {};
    if (module) {
      whereClause.module = module;
    }

    const permissions = await Permission.findAll({
      where: whereClause,
      order: [['module', 'ASC'], ['action', 'ASC']]
    });

    let result = permissions;

    // تجميع الصلاحيات حسب الوحدة
    if (groupByModule === 'true') {
      const groupedPermissions = {};
      permissions.forEach(permission => {
        if (!groupedPermissions[permission.module]) {
          groupedPermissions[permission.module] = [];
        }
        groupedPermissions[permission.module].push(permission);
      });
      result = groupedPermissions;
    }

    res.json({
      success: true,
      data: { permissions: result }
    });

  } catch (error) {
    console.error('Get all permissions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch permissions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getAllPermissions
};

# دليل إعداد وتشغيل نظام ERP

## المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Node.js** (الإصدار 16 أو أحدث)
- **PostgreSQL** (الإصدار 12 أو أحدث)
- **npm** أو **yarn**

### 2. التحقق من التثبيت
```bash
node --version
npm --version
psql --version
```

## إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```sql
-- الاتصال بـ PostgreSQL كمستخدم postgres
psql -U postgres

-- إنشاء قاعدة البيانات
CREATE DATABASE heavy_equipment_erp;

-- إنشاء مستخدم جديد (اختياري)
CREATE USER erp_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE heavy_equipment_erp TO erp_user;

-- الخروج
\q
```

### 2. إعد<PERSON> متغيرات البيئة
```bash
# في مجلد backend
cp .env.example .env
```

قم بتحديث ملف `.env`:
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_NAME=heavy_equipment_erp
DB_USER=postgres
DB_PASSWORD=V@admin010

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRE=7d

# Email Configuration (اختياري للتنبيهات)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
```

## تثبيت المشروع

### 1. تثبيت dependencies للباك إند
```bash
cd backend
npm install
```

### 2. تثبيت dependencies للفرونت إند
```bash
cd ../frontend
npm install
```

## إعداد قاعدة البيانات

### 1. تشغيل Migrations
```bash
cd backend
npx sequelize-cli db:migrate
```

### 2. تشغيل Seeders (البيانات الأساسية)
```bash
# إنشاء الصلاحيات والأدوار
npx sequelize-cli db:seed --seed 001-permissions-and-roles.js

# إنشاء مستخدم admin افتراضي
npx sequelize-cli db:seed --seed 002-admin-user.js
```

## تشغيل المشروع

### 1. تشغيل الباك إند
```bash
cd backend
npm run dev
```
الخادم سيعمل على: `http://localhost:5000`

### 2. تشغيل الفرونت إند (في terminal منفصل)
```bash
cd frontend
npm start
```
التطبيق سيعمل على: `http://localhost:3000`

## بيانات تسجيل الدخول الافتراضية

بعد تشغيل seeders، يمكنك تسجيل الدخول باستخدام:
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Admin123!`

⚠️ **مهم**: قم بتغيير كلمة المرور الافتراضية فور تسجيل الدخول!

## اختبار النظام

### 1. اختبار الباك إند
```bash
cd backend
npm test
```

### 2. اختبار APIs باستخدام curl
```bash
# تسجيل الدخول
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}'

# الحصول على الملف الشخصي (استبدل TOKEN بالتوكن المُستلم)
curl -X GET http://localhost:5000/api/auth/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## الأدوار والصلاحيات الافتراضية

### الأدوار المُنشأة:
1. **مدير النظام** (`admin`) - صلاحيات كاملة
2. **مدير عام** (`manager`) - معظم الصلاحيات
3. **محاسب** (`accountant`) - صلاحيات الحسابات والتقارير
4. **فني صيانة** (`technician`) - صلاحيات الصيانة والمعدات
5. **أمين مخزن** (`warehouse`) - صلاحيات المخزون
6. **مستخدم عادي** (`user`) - صلاحيات القراءة فقط

### الوحدات والصلاحيات:
- **المستخدمين** (`users`): create, read, update, delete
- **المعدات** (`equipment`): create, read, update, delete
- **المخزون** (`inventory`): create, read, update, delete, import, export
- **الصيانة** (`maintenance`): create, read, update, delete, approve
- **الحسابات** (`accounting`): create, read, update, delete, approve
- **التقارير** (`reports`): read, export
- **الإعدادات** (`settings`): read, update

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل PostgreSQL
   - تحقق من بيانات الاتصال في `.env`

2. **خطأ في JWT**
   - تأكد من وجود `JWT_SECRET` في `.env`
   - تأكد من أن التوكن صحيح وغير منتهي الصلاحية

3. **خطأ في Migrations**
   - تأكد من وجود قاعدة البيانات
   - تحقق من صلاحيات المستخدم

### سجلات الأخطاء:
- سجلات الباك إند: تظهر في terminal
- سجلات الفرونت إند: تظهر في browser console

## الخطوات التالية

بعد إعداد النظام بنجاح، يمكنك:
1. إنشاء مستخدمين جدد
2. تخصيص الأدوار والصلاحيات
3. إضافة المعدات والمخزون
4. إنشاء أوامر صيانة
5. إدارة الفواتير والحسابات

## الدعم

في حالة مواجهة مشاكل:
1. تحقق من سجلات الأخطاء
2. راجع ملف `.env`
3. تأكد من تشغيل جميع الخدمات المطلوبة

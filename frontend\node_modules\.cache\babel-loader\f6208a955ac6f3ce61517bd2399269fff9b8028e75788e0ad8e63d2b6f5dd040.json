{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  sidebarOpen: true,\n  theme: 'light',\n  language: 'ar',\n  notifications: [],\n  loading: {\n    global: false,\n    components: {}\n  }\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: state => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setLanguage: (state, action) => {\n      state.language = action.payload;\n      localStorage.setItem('language', action.payload);\n    },\n    addNotification: (state, action) => {\n      const notification = {\n        id: Date.now(),\n        type: 'info',\n        title: '',\n        message: '',\n        duration: 5000,\n        ...action.payload\n      };\n      state.notifications.push(notification);\n    },\n    removeNotification: (state, action) => {\n      state.notifications = state.notifications.filter(notification => notification.id !== action.payload);\n    },\n    clearNotifications: state => {\n      state.notifications = [];\n    },\n    setGlobalLoading: (state, action) => {\n      state.loading.global = action.payload;\n    },\n    setComponentLoading: (state, action) => {\n      const {\n        component,\n        loading\n      } = action.payload;\n      state.loading.components[component] = loading;\n    },\n    clearComponentLoading: (state, action) => {\n      delete state.loading.components[action.payload];\n    }\n  }\n});\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  setLanguage,\n  addNotification,\n  removeNotification,\n  clearNotifications,\n  setGlobalLoading,\n  setComponentLoading,\n  clearComponentLoading\n} = uiSlice.actions;\n\n// Selectors\nexport const selectUI = state => state.ui;\nexport const selectSidebarOpen = state => state.ui.sidebarOpen;\nexport const selectTheme = state => state.ui.theme;\nexport const selectLanguage = state => state.ui.language;\nexport const selectNotifications = state => state.ui.notifications;\nexport const selectGlobalLoading = state => state.ui.loading.global;\nexport const selectComponentLoading = component => state => state.ui.loading.components[component] || false;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarOpen", "theme", "language", "notifications", "loading", "global", "components", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarOpen", "action", "payload", "setTheme", "localStorage", "setItem", "setLanguage", "addNotification", "notification", "id", "Date", "now", "type", "title", "message", "duration", "push", "removeNotification", "filter", "clearNotifications", "setGlobalLoading", "setComponentLoading", "component", "clearComponentLoading", "actions", "selectUI", "ui", "selectSidebarOpen", "selectTheme", "selectLanguage", "selectNotifications", "selectGlobalLoading", "selectComponentLoading", "reducer"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/store/slices/uiSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\n\nconst initialState = {\n  sidebarOpen: true,\n  theme: 'light',\n  language: 'ar',\n  notifications: [],\n  loading: {\n    global: false,\n    components: {},\n  },\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setLanguage: (state, action) => {\n      state.language = action.payload;\n      localStorage.setItem('language', action.payload);\n    },\n    addNotification: (state, action) => {\n      const notification = {\n        id: Date.now(),\n        type: 'info',\n        title: '',\n        message: '',\n        duration: 5000,\n        ...action.payload,\n      };\n      state.notifications.push(notification);\n    },\n    removeNotification: (state, action) => {\n      state.notifications = state.notifications.filter(\n        (notification) => notification.id !== action.payload\n      );\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n    setGlobalLoading: (state, action) => {\n      state.loading.global = action.payload;\n    },\n    setComponentLoading: (state, action) => {\n      const { component, loading } = action.payload;\n      state.loading.components[component] = loading;\n    },\n    clearComponentLoading: (state, action) => {\n      delete state.loading.components[action.payload];\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  setLanguage,\n  addNotification,\n  removeNotification,\n  clearNotifications,\n  setGlobalLoading,\n  setComponentLoading,\n  clearComponentLoading,\n} = uiSlice.actions;\n\n// Selectors\nexport const selectUI = (state) => state.ui;\nexport const selectSidebarOpen = (state) => state.ui.sidebarOpen;\nexport const selectTheme = (state) => state.ui.theme;\nexport const selectLanguage = (state) => state.ui.language;\nexport const selectNotifications = (state) => state.ui.notifications;\nexport const selectGlobalLoading = (state) => state.ui.loading.global;\nexport const selectComponentLoading = (component) => (state) => \n  state.ui.loading.components[component] || false;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAE9C,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE;IACPC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,CAAC;EACf;AACF,CAAC;AAED,MAAMC,OAAO,GAAGT,WAAW,CAAC;EAC1BU,IAAI,EAAE,IAAI;EACVT,YAAY;EACZU,QAAQ,EAAE;IACRC,aAAa,EAAGC,KAAK,IAAK;MACxBA,KAAK,CAACX,WAAW,GAAG,CAACW,KAAK,CAACX,WAAW;IACxC,CAAC;IACDY,cAAc,EAAEA,CAACD,KAAK,EAAEE,MAAM,KAAK;MACjCF,KAAK,CAACX,WAAW,GAAGa,MAAM,CAACC,OAAO;IACpC,CAAC;IACDC,QAAQ,EAAEA,CAACJ,KAAK,EAAEE,MAAM,KAAK;MAC3BF,KAAK,CAACV,KAAK,GAAGY,MAAM,CAACC,OAAO;MAC5BE,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,MAAM,CAACC,OAAO,CAAC;IAC/C,CAAC;IACDI,WAAW,EAAEA,CAACP,KAAK,EAAEE,MAAM,KAAK;MAC9BF,KAAK,CAACT,QAAQ,GAAGW,MAAM,CAACC,OAAO;MAC/BE,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEJ,MAAM,CAACC,OAAO,CAAC;IAClD,CAAC;IACDK,eAAe,EAAEA,CAACR,KAAK,EAAEE,MAAM,KAAK;MAClC,MAAMO,YAAY,GAAG;QACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,QAAQ,EAAE,IAAI;QACd,GAAGd,MAAM,CAACC;MACZ,CAAC;MACDH,KAAK,CAACR,aAAa,CAACyB,IAAI,CAACR,YAAY,CAAC;IACxC,CAAC;IACDS,kBAAkB,EAAEA,CAAClB,KAAK,EAAEE,MAAM,KAAK;MACrCF,KAAK,CAACR,aAAa,GAAGQ,KAAK,CAACR,aAAa,CAAC2B,MAAM,CAC7CV,YAAY,IAAKA,YAAY,CAACC,EAAE,KAAKR,MAAM,CAACC,OAC/C,CAAC;IACH,CAAC;IACDiB,kBAAkB,EAAGpB,KAAK,IAAK;MAC7BA,KAAK,CAACR,aAAa,GAAG,EAAE;IAC1B,CAAC;IACD6B,gBAAgB,EAAEA,CAACrB,KAAK,EAAEE,MAAM,KAAK;MACnCF,KAAK,CAACP,OAAO,CAACC,MAAM,GAAGQ,MAAM,CAACC,OAAO;IACvC,CAAC;IACDmB,mBAAmB,EAAEA,CAACtB,KAAK,EAAEE,MAAM,KAAK;MACtC,MAAM;QAAEqB,SAAS;QAAE9B;MAAQ,CAAC,GAAGS,MAAM,CAACC,OAAO;MAC7CH,KAAK,CAACP,OAAO,CAACE,UAAU,CAAC4B,SAAS,CAAC,GAAG9B,OAAO;IAC/C,CAAC;IACD+B,qBAAqB,EAAEA,CAACxB,KAAK,EAAEE,MAAM,KAAK;MACxC,OAAOF,KAAK,CAACP,OAAO,CAACE,UAAU,CAACO,MAAM,CAACC,OAAO,CAAC;IACjD;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXJ,aAAa;EACbE,cAAc;EACdG,QAAQ;EACRG,WAAW;EACXC,eAAe;EACfU,kBAAkB;EAClBE,kBAAkB;EAClBC,gBAAgB;EAChBC,mBAAmB;EACnBE;AACF,CAAC,GAAG5B,OAAO,CAAC6B,OAAO;;AAEnB;AACA,OAAO,MAAMC,QAAQ,GAAI1B,KAAK,IAAKA,KAAK,CAAC2B,EAAE;AAC3C,OAAO,MAAMC,iBAAiB,GAAI5B,KAAK,IAAKA,KAAK,CAAC2B,EAAE,CAACtC,WAAW;AAChE,OAAO,MAAMwC,WAAW,GAAI7B,KAAK,IAAKA,KAAK,CAAC2B,EAAE,CAACrC,KAAK;AACpD,OAAO,MAAMwC,cAAc,GAAI9B,KAAK,IAAKA,KAAK,CAAC2B,EAAE,CAACpC,QAAQ;AAC1D,OAAO,MAAMwC,mBAAmB,GAAI/B,KAAK,IAAKA,KAAK,CAAC2B,EAAE,CAACnC,aAAa;AACpE,OAAO,MAAMwC,mBAAmB,GAAIhC,KAAK,IAAKA,KAAK,CAAC2B,EAAE,CAAClC,OAAO,CAACC,MAAM;AACrE,OAAO,MAAMuC,sBAAsB,GAAIV,SAAS,IAAMvB,KAAK,IACzDA,KAAK,CAAC2B,EAAE,CAAClC,OAAO,CAACE,UAAU,CAAC4B,SAAS,CAAC,IAAI,KAAK;AAEjD,eAAe3B,OAAO,CAACsC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
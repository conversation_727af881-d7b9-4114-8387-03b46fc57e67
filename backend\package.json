{"name": "heavy-equipment-erp-backend", "version": "1.0.0", "description": "Backend for Heavy Equipment ERP System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "sequelize-cli db:migrate", "seed": "sequelize-cli db:seed:all"}, "keywords": ["erp", "heavy-equipment", "maintenance", "inventory", "accounting"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sequelize": "^6.35.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "multer": "^1.4.5-lts.1", "exceljs": "^4.4.0", "nodemailer": "^6.9.8", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=16.0.0"}}
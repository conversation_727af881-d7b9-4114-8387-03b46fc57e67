{"name": "heavy-equipment-erp-backend", "version": "1.0.0", "description": "Backend for Heavy Equipment ERP System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "sequelize-cli db:migrate", "seed": "sequelize-cli db:seed:all"}, "keywords": ["erp", "heavy-equipment", "maintenance", "inventory", "accounting"], "author": "Your Name", "license": "MIT", "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}
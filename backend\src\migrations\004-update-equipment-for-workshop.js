'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // إضافة أعمدة جديدة لجدول المعدات لتناسب ورشة الصيانة
      await queryInterface.addColumn('equipment', 'owner_type', {
        type: Sequelize.ENUM('customer', 'workshop'),
        defaultValue: 'customer',
        allowNull: false
      }, { transaction });

      await queryInterface.addColumn('equipment', 'current_location', {
        type: Sequelize.ENUM('workshop', 'customer', 'field'),
        defaultValue: 'customer',
        allowNull: false
      }, { transaction });

      await queryInterface.addColumn('equipment', 'workshop_entry_date', {
        type: Sequelize.DATEONLY,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('equipment', 'workshop_exit_date', {
        type: Sequelize.DATEONLY,
        allowNull: true
      }, { transaction });

      await queryInterface.addColumn('equipment', 'engine_hours', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ساعات المحرك عند الاستلام'
      }, { transaction });

      await queryInterface.addColumn('equipment', 'fuel_level', {
        type: Sequelize.ENUM('empty', 'quarter', 'half', 'three_quarters', 'full'),
        allowNull: true,
        comment: 'مستوى الوقود عند الاستلام'
      }, { transaction });

      await queryInterface.addColumn('equipment', 'general_condition_notes', {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'ملاحظات الحالة العامة عند الاستلام'
      }, { transaction });

      // تحديث enum للحالة لتشمل حالات ورشة الصيانة
      await queryInterface.sequelize.query(`
        ALTER TYPE "enum_equipment_status" ADD VALUE 'in_workshop';
        ALTER TYPE "enum_equipment_status" ADD VALUE 'waiting_approval';
        ALTER TYPE "enum_equipment_status" ADD VALUE 'waiting_parts';
        ALTER TYPE "enum_equipment_status" ADD VALUE 'ready_for_delivery';
      `, { transaction });

      // إضافة فهارس جديدة
      await queryInterface.addIndex('equipment', ['owner_type'], { transaction });
      await queryInterface.addIndex('equipment', ['current_location'], { transaction });
      await queryInterface.addIndex('equipment', ['workshop_entry_date'], { transaction });

      await transaction.commit();
      console.log('✅ Equipment table updated for workshop successfully');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error updating equipment table:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // حذف الأعمدة المضافة
      await queryInterface.removeColumn('equipment', 'general_condition_notes', { transaction });
      await queryInterface.removeColumn('equipment', 'fuel_level', { transaction });
      await queryInterface.removeColumn('equipment', 'engine_hours', { transaction });
      await queryInterface.removeColumn('equipment', 'workshop_exit_date', { transaction });
      await queryInterface.removeColumn('equipment', 'workshop_entry_date', { transaction });
      await queryInterface.removeColumn('equipment', 'current_location', { transaction });
      await queryInterface.removeColumn('equipment', 'owner_type', { transaction });
      
      await transaction.commit();
      console.log('✅ Equipment table reverted successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error reverting equipment table:', error);
      throw error;
    }
  }
};

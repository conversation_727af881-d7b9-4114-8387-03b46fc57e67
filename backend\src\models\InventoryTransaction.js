const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InventoryTransaction = sequelize.define('InventoryTransaction', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    transactionNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'transaction_number',
      validate: {
        len: [5, 50],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM,
      values: ['in', 'out', 'adjustment', 'transfer'],
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    subtype: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [2, 50],
        isIn: [['purchase', 'sale', 'return', 'damage', 'loss', 'found', 'production', 'consumption', 'transfer_in', 'transfer_out', 'opening_balance', 'closing_balance']]
      }
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        notEmpty: true,
        min: 0.01
      }
    },
    unitCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'unit_cost',
      validate: {
        min: 0
      }
    },
    totalCost: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'total_cost',
      validate: {
        min: 0
      }
    },
    balanceBefore: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'balance_before',
      validate: {
        min: 0
      }
    },
    balanceAfter: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      field: 'balance_after',
      validate: {
        min: 0
      }
    },
    referenceType: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'reference_type',
      validate: {
        len: [2, 50],
        isIn: [['work_order', 'invoice', 'purchase_order', 'transfer', 'adjustment', 'manual']]
      }
    },
    referenceId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'reference_id'
    },
    referenceNumber: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'reference_number',
      validate: {
        len: [2, 100]
      }
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    transactionDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'transaction_date'
    },
    isReversed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_reversed'
    },
    reversedBy: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'reversed_by'
    },
    reversedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'reversed_at'
    },
    reverseReason: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'reverse_reason'
    }
  }, {
    tableName: 'inventory_transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['transaction_number']
      },
      {
        fields: ['item_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['transaction_date']
      },
      {
        fields: ['reference_type', 'reference_id']
      }
    ]
  });

  // Instance methods
  InventoryTransaction.prototype.canBeReversed = function() {
    return !this.isReversed && this.type !== 'adjustment';
  };

  InventoryTransaction.prototype.getTransactionDescription = function() {
    const typeDescriptions = {
      'in': 'إدخال',
      'out': 'إخراج',
      'adjustment': 'تسوية',
      'transfer': 'تحويل'
    };
    return typeDescriptions[this.type] || this.type;
  };

  // Associations
  InventoryTransaction.associate = function(models) {
    // InventoryTransaction belongs to InventoryItem
    InventoryTransaction.belongsTo(models.InventoryItem, {
      foreignKey: 'item_id',
      as: 'item'
    });

    // InventoryTransaction belongs to User (created by)
    InventoryTransaction.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // InventoryTransaction belongs to User (reversed by)
    InventoryTransaction.belongsTo(models.User, {
      foreignKey: 'reversed_by',
      as: 'reverser'
    });
  };

  return InventoryTransaction;
};

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Fady\\\\frontend\\\\src\\\\components\\\\UI\\\\NotificationContainer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { selectNotifications, removeNotification } from '../../store/slices/uiSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationContainer = () => {\n  _s();\n  const dispatch = useDispatch();\n  const notifications = useSelector(selectNotifications);\n  const getIcon = type => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"h-6 w-6 text-green-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(XCircleIcon, {\n          className: \"h-6 w-6 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n          className: \"h-6 w-6 text-yellow-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n          className: \"h-6 w-6 text-blue-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getBackgroundColor = type => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200';\n      default:\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n  const handleRemove = id => {\n    dispatch(removeNotification(id));\n  };\n\n  // إزالة الإشعارات تلقائياً بعد المدة المحددة\n  useEffect(() => {\n    notifications.forEach(notification => {\n      if (notification.duration > 0) {\n        const timer = setTimeout(() => {\n          dispatch(removeNotification(notification.id));\n        }, notification.duration);\n        return () => clearTimeout(timer);\n      }\n    });\n  }, [notifications, dispatch]);\n  if (notifications.length === 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 left-4 z-50 space-y-2 max-w-sm w-full\",\n    children: notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${getBackgroundColor(notification.type)} border rounded-lg shadow-lg p-4 animate-fade-in`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: getIcon(notification.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mr-3 flex-1\",\n          children: [notification.title && /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-900 mb-1\",\n            children: notification.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-700\",\n            children: notification.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0 mr-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRemove(notification.id),\n            className: \"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none\",\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)\n    }, notification.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationContainer, \"88B2NZq7afNVAdoB+f4aqkog56M=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = NotificationContainer;\nexport default NotificationContainer;\nvar _c;\n$RefreshReg$(_c, \"NotificationContainer\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useDispatch", "CheckCircleIcon", "ExclamationTriangleIcon", "InformationCircleIcon", "XCircleIcon", "XMarkIcon", "selectNotifications", "removeNotification", "jsxDEV", "_jsxDEV", "NotificationContainer", "_s", "dispatch", "notifications", "getIcon", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getBackgroundColor", "handleRemove", "id", "for<PERSON>ach", "notification", "duration", "timer", "setTimeout", "clearTimeout", "length", "children", "map", "title", "message", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/components/UI/NotificationContainer.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport {\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  InformationCircleIcon,\n  XCircleIcon,\n  XMarkIcon,\n} from '@heroicons/react/24/outline';\nimport { selectNotifications, removeNotification } from '../../store/slices/uiSlice';\n\nconst NotificationContainer = () => {\n  const dispatch = useDispatch();\n  const notifications = useSelector(selectNotifications);\n\n  const getIcon = (type) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircleIcon className=\"h-6 w-6 text-green-400\" />;\n      case 'error':\n        return <XCircleIcon className=\"h-6 w-6 text-red-400\" />;\n      case 'warning':\n        return <ExclamationTriangleIcon className=\"h-6 w-6 text-yellow-400\" />;\n      default:\n        return <InformationCircleIcon className=\"h-6 w-6 text-blue-400\" />;\n    }\n  };\n\n  const getBackgroundColor = (type) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200';\n      default:\n        return 'bg-blue-50 border-blue-200';\n    }\n  };\n\n  const handleRemove = (id) => {\n    dispatch(removeNotification(id));\n  };\n\n  // إزالة الإشعارات تلقائياً بعد المدة المحددة\n  useEffect(() => {\n    notifications.forEach((notification) => {\n      if (notification.duration > 0) {\n        const timer = setTimeout(() => {\n          dispatch(removeNotification(notification.id));\n        }, notification.duration);\n\n        return () => clearTimeout(timer);\n      }\n    });\n  }, [notifications, dispatch]);\n\n  if (notifications.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed top-4 left-4 z-50 space-y-2 max-w-sm w-full\">\n      {notifications.map((notification) => (\n        <div\n          key={notification.id}\n          className={`${getBackgroundColor(notification.type)} border rounded-lg shadow-lg p-4 animate-fade-in`}\n        >\n          <div className=\"flex items-start\">\n            <div className=\"flex-shrink-0\">\n              {getIcon(notification.type)}\n            </div>\n            <div className=\"mr-3 flex-1\">\n              {notification.title && (\n                <h3 className=\"text-sm font-medium text-gray-900 mb-1\">\n                  {notification.title}\n                </h3>\n              )}\n              <p className=\"text-sm text-gray-700\">{notification.message}</p>\n            </div>\n            <div className=\"flex-shrink-0 mr-2\">\n              <button\n                onClick={() => handleRemove(notification.id)}\n                className=\"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none\"\n              >\n                <XMarkIcon className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default NotificationContainer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,eAAe,EACfC,uBAAuB,EACvBC,qBAAqB,EACrBC,WAAW,EACXC,SAAS,QACJ,6BAA6B;AACpC,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,aAAa,GAAGd,WAAW,CAACO,mBAAmB,CAAC;EAEtD,MAAMQ,OAAO,GAAIC,IAAI,IAAK;IACxB,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,oBAAON,OAAA,CAACR,eAAe;UAACe,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACL,WAAW;UAACY,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,SAAS;QACZ,oBAAOX,OAAA,CAACP,uBAAuB;UAACc,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxE;QACE,oBAAOX,OAAA,CAACN,qBAAqB;UAACa,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACtE;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIN,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,8BAA8B;MACvC,KAAK,OAAO;QACV,OAAO,0BAA0B;MACnC,KAAK,SAAS;QACZ,OAAO,gCAAgC;MACzC;QACE,OAAO,4BAA4B;IACvC;EACF,CAAC;EAED,MAAMO,YAAY,GAAIC,EAAE,IAAK;IAC3BX,QAAQ,CAACL,kBAAkB,CAACgB,EAAE,CAAC,CAAC;EAClC,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACde,aAAa,CAACW,OAAO,CAAEC,YAAY,IAAK;MACtC,IAAIA,YAAY,CAACC,QAAQ,GAAG,CAAC,EAAE;QAC7B,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;UAC7BhB,QAAQ,CAACL,kBAAkB,CAACkB,YAAY,CAACF,EAAE,CAAC,CAAC;QAC/C,CAAC,EAAEE,YAAY,CAACC,QAAQ,CAAC;QAEzB,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,aAAa,EAAED,QAAQ,CAAC,CAAC;EAE7B,IAAIC,aAAa,CAACiB,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,oBACErB,OAAA;IAAKO,SAAS,EAAC,mDAAmD;IAAAe,QAAA,EAC/DlB,aAAa,CAACmB,GAAG,CAAEP,YAAY,iBAC9BhB,OAAA;MAEEO,SAAS,EAAE,GAAGK,kBAAkB,CAACI,YAAY,CAACV,IAAI,CAAC,kDAAmD;MAAAgB,QAAA,eAEtGtB,OAAA;QAAKO,SAAS,EAAC,kBAAkB;QAAAe,QAAA,gBAC/BtB,OAAA;UAAKO,SAAS,EAAC,eAAe;UAAAe,QAAA,EAC3BjB,OAAO,CAACW,YAAY,CAACV,IAAI;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACNX,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAe,QAAA,GACzBN,YAAY,CAACQ,KAAK,iBACjBxB,OAAA;YAAIO,SAAS,EAAC,wCAAwC;YAAAe,QAAA,EACnDN,YAAY,CAACQ;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACL,eACDX,OAAA;YAAGO,SAAS,EAAC,uBAAuB;YAAAe,QAAA,EAAEN,YAAY,CAACS;UAAO;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNX,OAAA;UAAKO,SAAS,EAAC,oBAAoB;UAAAe,QAAA,eACjCtB,OAAA;YACE0B,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAACG,YAAY,CAACF,EAAE,CAAE;YAC7CP,SAAS,EAAC,kEAAkE;YAAAe,QAAA,eAE5EtB,OAAA,CAACJ,SAAS;cAACW,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAvBDK,YAAY,CAACF,EAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBjB,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACT,EAAA,CAnFID,qBAAqB;EAAA,QACRV,WAAW,EACND,WAAW;AAAA;AAAAqC,EAAA,GAF7B1B,qBAAqB;AAqF3B,eAAeA,qBAAqB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
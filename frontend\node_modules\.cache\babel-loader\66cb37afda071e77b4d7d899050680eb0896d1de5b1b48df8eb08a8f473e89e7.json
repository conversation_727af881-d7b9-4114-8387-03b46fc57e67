{"ast": null, "code": "import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider(_ref) {\n  let {\n    i18n,\n    defaultNS,\n    children\n  } = _ref;\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}", "map": {"version": 3, "names": ["createElement", "useMemo", "I18nContext", "I18nextProvider", "_ref", "i18n", "defaultNS", "children", "value", "Provider"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/node_modules/react-i18next/dist/es/I18nextProvider.js"], "sourcesContent": ["import { createElement, useMemo } from 'react';\nimport { I18nContext } from './context.js';\nexport function I18nextProvider(_ref) {\n  let {\n    i18n,\n    defaultNS,\n    children\n  } = _ref;\n  const value = useMemo(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return createElement(I18nContext.Provider, {\n    value\n  }, children);\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,OAAO,QAAQ,OAAO;AAC9C,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,IAAI;IACFC,IAAI;IACJC,SAAS;IACTC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,KAAK,GAAGP,OAAO,CAAC,OAAO;IAC3BI,IAAI;IACJC;EACF,CAAC,CAAC,EAAE,CAACD,IAAI,EAAEC,SAAS,CAAC,CAAC;EACtB,OAAON,aAAa,CAACE,WAAW,CAACO,QAAQ,EAAE;IACzCD;EACF,CAAC,EAAED,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
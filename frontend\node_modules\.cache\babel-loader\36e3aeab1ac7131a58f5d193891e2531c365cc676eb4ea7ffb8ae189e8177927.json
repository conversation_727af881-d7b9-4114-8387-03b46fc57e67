{"ast": null, "code": "import axios from 'axios';\nimport { store } from '../store/store';\nimport { logout } from '../store/slices/authSlice';\nimport { addNotification } from '../store/slices/uiSlice';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor لإضافة التوكن\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor للتعامل مع الأخطاء\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  const {\n    response\n  } = error;\n  if (response) {\n    const {\n      status,\n      data\n    } = response;\n\n    // التعامل مع أخطاء المصادقة\n    if (status === 401) {\n      // إذا كان التوكن منتهي الصلاحية أو غير صحيح\n      if (data.message === 'Token expired' || data.message === 'Invalid token') {\n        store.dispatch(logout());\n        store.dispatch(addNotification({\n          type: 'error',\n          title: 'انتهت جلسة العمل',\n          message: 'يرجى تسجيل الدخول مرة أخرى',\n          duration: 5000\n        }));\n        // إعادة توجيه لصفحة تسجيل الدخول\n        window.location.href = '/login';\n      }\n    }\n\n    // التعامل مع أخطاء الصلاحيات\n    else if (status === 403) {\n      store.dispatch(addNotification({\n        type: 'error',\n        title: 'غير مصرح',\n        message: data.message || 'ليس لديك صلاحية للوصول لهذا المورد',\n        duration: 5000\n      }));\n    }\n\n    // التعامل مع أخطاء الخادم\n    else if (status >= 500) {\n      store.dispatch(addNotification({\n        type: 'error',\n        title: 'خطأ في الخادم',\n        message: 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً',\n        duration: 5000\n      }));\n    }\n\n    // التعامل مع أخطاء التحقق من صحة البيانات\n    else if (status === 400 && data.errors) {\n      const errorMessages = data.errors.map(err => err.message).join(', ');\n      store.dispatch(addNotification({\n        type: 'error',\n        title: 'خطأ في البيانات',\n        message: errorMessages,\n        duration: 7000\n      }));\n    }\n  } else if (error.request) {\n    // خطأ في الشبكة\n    store.dispatch(addNotification({\n      type: 'error',\n      title: 'خطأ في الاتصال',\n      message: 'تعذر الاتصال بالخادم، تحقق من اتصال الإنترنت',\n      duration: 5000\n    }));\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "store", "logout", "addNotification", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "status", "data", "message", "dispatch", "type", "title", "duration", "window", "location", "href", "errors", "errorMessages", "map", "err", "join"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport { store } from '../store/store';\nimport { logout } from '../store/slices/authSlice';\nimport { addNotification } from '../store/slices/uiSlice';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor لإضافة التوكن\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor للتعامل مع الأخطاء\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    const { response } = error;\n    \n    if (response) {\n      const { status, data } = response;\n      \n      // التعامل مع أخطاء المصادقة\n      if (status === 401) {\n        // إذا كان التوكن منتهي الصلاحية أو غير صحيح\n        if (data.message === 'Token expired' || data.message === 'Invalid token') {\n          store.dispatch(logout());\n          store.dispatch(addNotification({\n            type: 'error',\n            title: 'انتهت جلسة العمل',\n            message: 'يرجى تسجيل الدخول مرة أخرى',\n            duration: 5000,\n          }));\n          // إعادة توجيه لصفحة تسجيل الدخول\n          window.location.href = '/login';\n        }\n      }\n      \n      // التعامل مع أخطاء الصلاحيات\n      else if (status === 403) {\n        store.dispatch(addNotification({\n          type: 'error',\n          title: 'غير مصرح',\n          message: data.message || 'ليس لديك صلاحية للوصول لهذا المورد',\n          duration: 5000,\n        }));\n      }\n      \n      // التعامل مع أخطاء الخادم\n      else if (status >= 500) {\n        store.dispatch(addNotification({\n          type: 'error',\n          title: 'خطأ في الخادم',\n          message: 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً',\n          duration: 5000,\n        }));\n      }\n      \n      // التعامل مع أخطاء التحقق من صحة البيانات\n      else if (status === 400 && data.errors) {\n        const errorMessages = data.errors.map(err => err.message).join(', ');\n        store.dispatch(addNotification({\n          type: 'error',\n          title: 'خطأ في البيانات',\n          message: errorMessages,\n          duration: 7000,\n        }));\n      }\n    } else if (error.request) {\n      // خطأ في الشبكة\n      store.dispatch(addNotification({\n        type: 'error',\n        title: 'خطأ في الاتصال',\n        message: 'تعذر الاتصال بالخادم، تحقق من اتصال الإنترنت',\n        duration: 5000,\n      }));\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,MAAM,QAAQ,2BAA2B;AAClD,SAASC,eAAe,QAAQ,yBAAyB;;AAEzD;AACA,MAAMC,GAAG,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;EACrEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT,MAAM;IAAEG;EAAS,CAAC,GAAGH,KAAK;EAE1B,IAAIG,QAAQ,EAAE;IACZ,MAAM;MAAEC,MAAM;MAAEC;IAAK,CAAC,GAAGF,QAAQ;;IAEjC;IACA,IAAIC,MAAM,KAAK,GAAG,EAAE;MAClB;MACA,IAAIC,IAAI,CAACC,OAAO,KAAK,eAAe,IAAID,IAAI,CAACC,OAAO,KAAK,eAAe,EAAE;QACxEzB,KAAK,CAAC0B,QAAQ,CAACzB,MAAM,CAAC,CAAC,CAAC;QACxBD,KAAK,CAAC0B,QAAQ,CAACxB,eAAe,CAAC;UAC7ByB,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,kBAAkB;UACzBH,OAAO,EAAE,4BAA4B;UACrCI,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;QACH;QACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;IACF;;IAEA;IAAA,KACK,IAAIT,MAAM,KAAK,GAAG,EAAE;MACvBvB,KAAK,CAAC0B,QAAQ,CAACxB,eAAe,CAAC;QAC7ByB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,UAAU;QACjBH,OAAO,EAAED,IAAI,CAACC,OAAO,IAAI,oCAAoC;QAC7DI,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;;IAEA;IAAA,KACK,IAAIN,MAAM,IAAI,GAAG,EAAE;MACtBvB,KAAK,CAAC0B,QAAQ,CAACxB,eAAe,CAAC;QAC7ByB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,eAAe;QACtBH,OAAO,EAAE,yCAAyC;QAClDI,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;;IAEA;IAAA,KACK,IAAIN,MAAM,KAAK,GAAG,IAAIC,IAAI,CAACS,MAAM,EAAE;MACtC,MAAMC,aAAa,GAAGV,IAAI,CAACS,MAAM,CAACE,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACX,OAAO,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;MACpErC,KAAK,CAAC0B,QAAQ,CAACxB,eAAe,CAAC;QAC7ByB,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,iBAAiB;QACxBH,OAAO,EAAES,aAAa;QACtBL,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,MAAM,IAAIV,KAAK,CAACP,OAAO,EAAE;IACxB;IACAZ,KAAK,CAAC0B,QAAQ,CAACxB,eAAe,CAAC;MAC7ByB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,gBAAgB;MACvBH,OAAO,EAAE,8CAA8C;MACvDI,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;EACL;EAEA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAehB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
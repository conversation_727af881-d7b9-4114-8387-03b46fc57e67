const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';
let authToken = '';
let testItemId = '';

async function runInventoryTests() {
  console.log('🧪 بدء اختبار API المخزون...\n');

  try {
    // 1. تسجيل الدخول
    console.log('1️⃣ اختبار تسجيل الدخول...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });
    
    authToken = loginResponse.data.data.token;
    console.log('✅ تم تسجيل الدخول بنجاح\n');

    // إعداد headers للطلبات القادمة
    const headers = { 'Authorization': `Bearer ${authToken}` };

    // 2. اختبار إحصائيات المخزون
    console.log('2️⃣ اختبار إحصائيات المخزون...');
    const statsResponse = await axios.get(`${BASE_URL}/inventory/stats`, { headers });
    console.log('✅ إحصائيات المخزون:', {
      totalItems: statsResponse.data.data.totalItems,
      lowStockItems: statsResponse.data.data.lowStockItems,
      outOfStockItems: statsResponse.data.data.outOfStockItems,
      totalValue: statsResponse.data.data.totalValue
    });
    console.log('');

    // 3. اختبار إنشاء عنصر مخزون جديد
    console.log('3️⃣ اختبار إنشاء عنصر مخزون جديد...');
    const newItem = {
      itemCode: 'TEST-001',
      name: 'قطعة غيار اختبار',
      description: 'قطعة غيار للاختبار',
      minimumStock: 10,
      maximumStock: 100,
      unitCost: 25.50,
      sellingPrice: 35.00,
      location: 'المخزن الرئيسي',
      barcode: '1234567890123',
      supplier: 'مورد الاختبار'
    };

    const createResponse = await axios.post(`${BASE_URL}/inventory/items`, newItem, { headers });
    testItemId = createResponse.data.data.id;
    console.log('✅ تم إنشاء عنصر المخزون:', {
      id: testItemId,
      name: createResponse.data.data.name,
      itemCode: createResponse.data.data.itemCode
    });
    console.log('');

    // 4. اختبار الحصول على تفاصيل العنصر
    console.log('4️⃣ اختبار الحصول على تفاصيل العنصر...');
    const itemResponse = await axios.get(`${BASE_URL}/inventory/items/${testItemId}`, { headers });
    console.log('✅ تفاصيل العنصر:', {
      name: itemResponse.data.data.name,
      currentStock: itemResponse.data.data.currentStock,
      unitCost: itemResponse.data.data.unitCost
    });
    console.log('');

    // 5. اختبار تحديث مخزون العنصر (إدخال)
    console.log('5️⃣ اختبار تحديث مخزون العنصر (إدخال)...');
    const stockInResponse = await axios.post(`${BASE_URL}/inventory/items/${testItemId}/stock`, {
      quantity: 50,
      type: 'in',
      unitCost: 25.50,
      reference: 'PO-001',
      notes: 'إدخال مخزون أولي'
    }, { headers });
    console.log('✅ تم إدخال المخزون:', {
      newStock: stockInResponse.data.data.currentStock
    });
    console.log('');

    // 6. اختبار تحديث مخزون العنصر (إخراج)
    console.log('6️⃣ اختبار تحديث مخزون العنصر (إخراج)...');
    const stockOutResponse = await axios.post(`${BASE_URL}/inventory/items/${testItemId}/stock`, {
      quantity: 15,
      type: 'out',
      reference: 'WO-001',
      notes: 'استخدام في أمر عمل'
    }, { headers });
    console.log('✅ تم إخراج المخزون:', {
      newStock: stockOutResponse.data.data.currentStock
    });
    console.log('');

    // 7. اختبار الحصول على جميع عناصر المخزون
    console.log('7️⃣ اختبار الحصول على جميع عناصر المخزون...');
    const allItemsResponse = await axios.get(`${BASE_URL}/inventory/items?page=1&limit=5`, { headers });
    console.log('✅ عناصر المخزون:', {
      totalItems: allItemsResponse.data.data.pagination.totalItems,
      currentPage: allItemsResponse.data.data.pagination.currentPage,
      itemsCount: allItemsResponse.data.data.items.length
    });
    console.log('');

    // 8. اختبار البحث في المخزون
    console.log('8️⃣ اختبار البحث في المخزون...');
    const searchResponse = await axios.get(`${BASE_URL}/inventory/items?search=اختبار`, { headers });
    console.log('✅ نتائج البحث:', {
      foundItems: searchResponse.data.data.items.length
    });
    console.log('');

    // 9. اختبار تحديث عنصر المخزون
    console.log('9️⃣ اختبار تحديث عنصر المخزون...');
    const updateResponse = await axios.put(`${BASE_URL}/inventory/items/${testItemId}`, {
      name: 'قطعة غيار اختبار محدثة',
      unitCost: 30.00,
      sellingPrice: 40.00
    }, { headers });
    console.log('✅ تم تحديث العنصر:', {
      name: updateResponse.data.data.name,
      unitCost: updateResponse.data.data.unitCost
    });
    console.log('');

    // 10. اختبار حذف عنصر المخزون
    console.log('🔟 اختبار حذف عنصر المخزون...');
    await axios.delete(`${BASE_URL}/inventory/items/${testItemId}`, { headers });
    console.log('✅ تم حذف العنصر بنجاح');
    console.log('');

    console.log('🎉 جميع اختبارات API المخزون نجحت!');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
  }
}

// تشغيل الاختبارات
runInventoryTests();

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const UnitOfMeasure = sequelize.define('UnitOfMeasure', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 50],
        notEmpty: true
      }
    },
    nameAr: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'name_ar',
      validate: {
        len: [2, 50]
      }
    },
    abbreviation: {
      type: DataTypes.STRING(10),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 10],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM('weight', 'length', 'volume', 'area', 'count', 'time'),
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'units_of_measure',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['name']
      },
      {
        fields: ['abbreviation']
      },
      {
        fields: ['type']
      }
    ]
  });

  // Instance methods
  UnitOfMeasure.prototype.getDisplayName = function() {
    return `${this.name} (${this.abbreviation})`;
  };

  // Associations
  UnitOfMeasure.associate = function(models) {
    // UnitOfMeasure has many InventoryItems
    if (models.InventoryItem) {
      UnitOfMeasure.hasMany(models.InventoryItem, {
        foreignKey: 'unit_id',
        as: 'items'
      });
    }
  };

  return UnitOfMeasure;
};

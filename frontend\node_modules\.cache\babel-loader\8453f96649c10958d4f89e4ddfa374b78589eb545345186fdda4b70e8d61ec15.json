{"ast": null, "code": "import _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nvar serializeCookie = function serializeCookie(name, val, options) {\n  var opt = options || {};\n  opt.path = opt.path || '/';\n  var value = encodeURIComponent(val);\n  var str = \"\".concat(name, \"=\").concat(value);\n  if (opt.maxAge > 0) {\n    var maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += \"; Max-Age=\".concat(Math.floor(maxAge));\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += \"; Domain=\".concat(opt.domain);\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += \"; Path=\".concat(opt.path);\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += \"; Expires=\".concat(opt.expires.toUTCString());\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n};\nvar cookie = {\n  create: function create(name, value, minutes, domain) {\n    var cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read: function read(name) {\n    var nameEQ = \"\".concat(name, \"=\");\n    var ca = document.cookie.split(';');\n    for (var i = 0; i < ca.length; i++) {\n      var c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove: function remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      var c = cookie.read(options.lookupCookie);\n      if (c) found = c;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      cookie.create(options.lookupCookie, lng, options.cookieMinutes, options.cookieDomain, options.cookieOptions);\n    }\n  }\n};\nvar querystring = {\n  name: 'querystring',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var search = window.location.search;\n      if (!window.location.search && window.location.hash && window.location.hash.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      var query = search.substring(1);\n      var params = query.split('&');\n      for (var i = 0; i < params.length; i++) {\n        var pos = params[i].indexOf('=');\n        if (pos > 0) {\n          var key = params[i].substring(0, pos);\n          if (key === options.lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\nvar hasLocalStorageSupport = null;\nvar localStorageAvailable = function localStorageAvailable() {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = window !== 'undefined' && window.localStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      var lng = window.localStorage.getItem(options.lookupLocalStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(options.lookupLocalStorage, lng);\n    }\n  }\n};\nvar hasSessionStorageSupport = null;\nvar sessionStorageAvailable = function sessionStorageAvailable() {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = window !== 'undefined' && window.sessionStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      var lng = window.sessionStorage.getItem(options.lookupSessionStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(options.lookupSessionStorage, lng);\n    }\n  }\n};\nvar navigator$1 = {\n  name: 'navigator',\n  lookup: function lookup(options) {\n    var found = [];\n    if (typeof navigator !== 'undefined') {\n      if (navigator.languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (var i = 0; i < navigator.languages.length; i++) {\n          found.push(navigator.languages[i]);\n        }\n      }\n      if (navigator.userLanguage) {\n        found.push(navigator.userLanguage);\n      }\n      if (navigator.language) {\n        found.push(navigator.language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\nvar htmlTag = {\n  name: 'htmlTag',\n  lookup: function lookup(options) {\n    var found;\n    var htmlTag = options.htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (htmlTag && typeof htmlTag.getAttribute === 'function') {\n      found = htmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\nvar path = {\n  name: 'path',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n      if (language instanceof Array) {\n        if (typeof options.lookupFromPathIndex === 'number') {\n          if (typeof language[options.lookupFromPathIndex] !== 'string') {\n            return undefined;\n          }\n          found = language[options.lookupFromPathIndex].replace('/', '');\n        } else {\n          found = language[0].replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\nvar subdomain = {\n  name: 'subdomain',\n  lookup: function lookup(options) {\n    // If given get the subdomain index else 1\n    var lookupFromSubdomainIndex = typeof options.lookupFromSubdomainIndex === 'number' ? options.lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group macht which sould be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    var language = typeof window !== 'undefined' && window.location && window.location.hostname && window.location.hostname.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[lookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nvar canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nvar order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nfunction getDefaults() {\n  return {\n    order: order,\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n    lookupSessionStorage: 'i18nextLng',\n    // cache user language\n    caches: ['localStorage'],\n    excludeCacheFor: ['cimode'],\n    // cookieMinutes: 10,\n    // cookieDomain: 'myDomain'\n\n    convertDetectedLanguage: function convertDetectedLanguage(l) {\n      return l;\n    }\n  };\n}\nvar Browser = /*#__PURE__*/function () {\n  function Browser(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Browser);\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  return _createClass(Browser, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services || {\n        languageUtils: {}\n      }; // this way the language detector can be used without i18next\n      this.options = defaults(options, this.options || {}, getDefaults());\n      if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n        this.options.convertDetectedLanguage = function (l) {\n          return l.replace('-', '_');\n        };\n      }\n\n      // backwards compatibility\n      if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n      this.i18nOptions = i18nOptions;\n      this.addDetector(cookie$1);\n      this.addDetector(querystring);\n      this.addDetector(localStorage);\n      this.addDetector(sessionStorage);\n      this.addDetector(navigator$1);\n      this.addDetector(htmlTag);\n      this.addDetector(path);\n      this.addDetector(subdomain);\n    }\n  }, {\n    key: \"addDetector\",\n    value: function addDetector(detector) {\n      this.detectors[detector.name] = detector;\n      return this;\n    }\n  }, {\n    key: \"detect\",\n    value: function detect(detectionOrder) {\n      var _this = this;\n      if (!detectionOrder) detectionOrder = this.options.order;\n      var detected = [];\n      detectionOrder.forEach(function (detectorName) {\n        if (_this.detectors[detectorName]) {\n          var lookup = _this.detectors[detectorName].lookup(_this.options);\n          if (lookup && typeof lookup === 'string') lookup = [lookup];\n          if (lookup) detected = detected.concat(lookup);\n        }\n      });\n      detected = detected.map(function (d) {\n        return _this.options.convertDetectedLanguage(d);\n      });\n      if (this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n      return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n    }\n  }, {\n    key: \"cacheUserLanguage\",\n    value: function cacheUserLanguage(lng, caches) {\n      var _this2 = this;\n      if (!caches) caches = this.options.caches;\n      if (!caches) return;\n      if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n      caches.forEach(function (cacheName) {\n        if (_this2.detectors[cacheName]) _this2.detectors[cacheName].cacheUserLanguage(lng, _this2.options);\n      });\n    }\n  }]);\n}();\nBrowser.type = 'languageDetector';\nexport { Browser as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "arr", "each", "for<PERSON>ach", "slice", "defaults", "obj", "call", "arguments", "source", "prop", "undefined", "fieldContentRegExp", "serializeCookie", "name", "val", "options", "opt", "path", "value", "encodeURIComponent", "str", "concat", "maxAge", "Number", "isNaN", "Error", "Math", "floor", "domain", "test", "TypeError", "expires", "toUTCString", "httpOnly", "secure", "sameSite", "toLowerCase", "cookie", "create", "minutes", "cookieOptions", "length", "Date", "setTime", "getTime", "document", "read", "nameEQ", "ca", "split", "i", "c", "char<PERSON>t", "substring", "indexOf", "remove", "cookie$1", "lookup", "found", "lookup<PERSON><PERSON><PERSON>", "cacheUserLanguage", "lng", "cookieMinutes", "cookieDomain", "querystring", "window", "search", "location", "hash", "query", "params", "pos", "key", "lookupQuerystring", "hasLocalStorageSupport", "localStorageAvailable", "localStorage", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "e", "lookupLocalStorage", "getItem", "hasSessionStorageSupport", "sessionStorageAvailable", "sessionStorage", "lookupSessionStorage", "navigator$1", "navigator", "languages", "push", "userLanguage", "language", "htmlTag", "documentElement", "getAttribute", "pathname", "match", "Array", "lookupFromPathIndex", "replace", "subdomain", "lookupFromSubdomainIndex", "hostname", "canCookies", "order", "splice", "getDefaults", "caches", "excludeCache<PERSON>or", "convertDetectedLanguage", "l", "Browser", "services", "type", "detectors", "init", "i18nOptions", "languageUtils", "lookupFromUrlIndex", "addDetector", "detector", "detect", "detectionOrder", "_this", "detected", "detectorName", "map", "d", "getBestMatchFromCodes", "_this2", "cacheName", "default"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["import _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\n\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nfunction defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\n\n// eslint-disable-next-line no-control-regex\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nvar serializeCookie = function serializeCookie(name, val, options) {\n  var opt = options || {};\n  opt.path = opt.path || '/';\n  var value = encodeURIComponent(val);\n  var str = \"\".concat(name, \"=\").concat(value);\n  if (opt.maxAge > 0) {\n    var maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += \"; Max-Age=\".concat(Math.floor(maxAge));\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += \"; Domain=\".concat(opt.domain);\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += \"; Path=\".concat(opt.path);\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += \"; Expires=\".concat(opt.expires.toUTCString());\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n};\nvar cookie = {\n  create: function create(name, value, minutes, domain) {\n    var cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, encodeURIComponent(value), cookieOptions);\n  },\n  read: function read(name) {\n    var nameEQ = \"\".concat(name, \"=\");\n    var ca = document.cookie.split(';');\n    for (var i = 0; i < ca.length; i++) {\n      var c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove: function remove(name) {\n    this.create(name, '', -1);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      var c = cookie.read(options.lookupCookie);\n      if (c) found = c;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupCookie && typeof document !== 'undefined') {\n      cookie.create(options.lookupCookie, lng, options.cookieMinutes, options.cookieDomain, options.cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var search = window.location.search;\n      if (!window.location.search && window.location.hash && window.location.hash.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      var query = search.substring(1);\n      var params = query.split('&');\n      for (var i = 0; i < params.length; i++) {\n        var pos = params[i].indexOf('=');\n        if (pos > 0) {\n          var key = params[i].substring(0, pos);\n          if (key === options.lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hasLocalStorageSupport = null;\nvar localStorageAvailable = function localStorageAvailable() {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = window !== 'undefined' && window.localStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      var lng = window.localStorage.getItem(options.lookupLocalStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(options.lookupLocalStorage, lng);\n    }\n  }\n};\n\nvar hasSessionStorageSupport = null;\nvar sessionStorageAvailable = function sessionStorageAvailable() {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = window !== 'undefined' && window.sessionStorage !== null;\n    var testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup: function lookup(options) {\n    var found;\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      var lng = window.sessionStorage.getItem(options.lookupSessionStorage);\n      if (lng) found = lng;\n    }\n    return found;\n  },\n  cacheUserLanguage: function cacheUserLanguage(lng, options) {\n    if (options.lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(options.lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup: function lookup(options) {\n    var found = [];\n    if (typeof navigator !== 'undefined') {\n      if (navigator.languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (var i = 0; i < navigator.languages.length; i++) {\n          found.push(navigator.languages[i]);\n        }\n      }\n      if (navigator.userLanguage) {\n        found.push(navigator.userLanguage);\n      }\n      if (navigator.language) {\n        found.push(navigator.language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  lookup: function lookup(options) {\n    var found;\n    var htmlTag = options.htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (htmlTag && typeof htmlTag.getAttribute === 'function') {\n      found = htmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  lookup: function lookup(options) {\n    var found;\n    if (typeof window !== 'undefined') {\n      var language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n      if (language instanceof Array) {\n        if (typeof options.lookupFromPathIndex === 'number') {\n          if (typeof language[options.lookupFromPathIndex] !== 'string') {\n            return undefined;\n          }\n          found = language[options.lookupFromPathIndex].replace('/', '');\n        } else {\n          found = language[0].replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup: function lookup(options) {\n    // If given get the subdomain index else 1\n    var lookupFromSubdomainIndex = typeof options.lookupFromSubdomainIndex === 'number' ? options.lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group macht which sould be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    var language = typeof window !== 'undefined' && window.location && window.location.hostname && window.location.hostname.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[lookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nvar canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nvar order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nfunction getDefaults() {\n  return {\n    order: order,\n    lookupQuerystring: 'lng',\n    lookupCookie: 'i18next',\n    lookupLocalStorage: 'i18nextLng',\n    lookupSessionStorage: 'i18nextLng',\n    // cache user language\n    caches: ['localStorage'],\n    excludeCacheFor: ['cimode'],\n    // cookieMinutes: 10,\n    // cookieDomain: 'myDomain'\n\n    convertDetectedLanguage: function convertDetectedLanguage(l) {\n      return l;\n    }\n  };\n}\nvar Browser = /*#__PURE__*/function () {\n  function Browser(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Browser);\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  return _createClass(Browser, [{\n    key: \"init\",\n    value: function init(services) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services || {\n        languageUtils: {}\n      }; // this way the language detector can be used without i18next\n      this.options = defaults(options, this.options || {}, getDefaults());\n      if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n        this.options.convertDetectedLanguage = function (l) {\n          return l.replace('-', '_');\n        };\n      }\n\n      // backwards compatibility\n      if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n      this.i18nOptions = i18nOptions;\n      this.addDetector(cookie$1);\n      this.addDetector(querystring);\n      this.addDetector(localStorage);\n      this.addDetector(sessionStorage);\n      this.addDetector(navigator$1);\n      this.addDetector(htmlTag);\n      this.addDetector(path);\n      this.addDetector(subdomain);\n    }\n  }, {\n    key: \"addDetector\",\n    value: function addDetector(detector) {\n      this.detectors[detector.name] = detector;\n      return this;\n    }\n  }, {\n    key: \"detect\",\n    value: function detect(detectionOrder) {\n      var _this = this;\n      if (!detectionOrder) detectionOrder = this.options.order;\n      var detected = [];\n      detectionOrder.forEach(function (detectorName) {\n        if (_this.detectors[detectorName]) {\n          var lookup = _this.detectors[detectorName].lookup(_this.options);\n          if (lookup && typeof lookup === 'string') lookup = [lookup];\n          if (lookup) detected = detected.concat(lookup);\n        }\n      });\n      detected = detected.map(function (d) {\n        return _this.options.convertDetectedLanguage(d);\n      });\n      if (this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n      return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n    }\n  }, {\n    key: \"cacheUserLanguage\",\n    value: function cacheUserLanguage(lng, caches) {\n      var _this2 = this;\n      if (!caches) caches = this.options.caches;\n      if (!caches) return;\n      if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n      caches.forEach(function (cacheName) {\n        if (_this2.detectors[cacheName]) _this2.detectors[cacheName].cacheUserLanguage(lng, _this2.options);\n      });\n    }\n  }]);\n}();\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AAEjE,IAAIC,GAAG,GAAG,EAAE;AACZ,IAAIC,IAAI,GAAGD,GAAG,CAACE,OAAO;AACtB,IAAIC,KAAK,GAAGH,GAAG,CAACG,KAAK;AACrB,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrBJ,IAAI,CAACK,IAAI,CAACH,KAAK,CAACG,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,EAAE,UAAUC,MAAM,EAAE;IACpD,IAAIA,MAAM,EAAE;MACV,KAAK,IAAIC,IAAI,IAAID,MAAM,EAAE;QACvB,IAAIH,GAAG,CAACI,IAAI,CAAC,KAAKC,SAAS,EAAEL,GAAG,CAACI,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MACvD;IACF;EACF,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ;;AAEA;AACA,IAAIM,kBAAkB,GAAG,uCAAuC;AAChE,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACjE,IAAIC,GAAG,GAAGD,OAAO,IAAI,CAAC,CAAC;EACvBC,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACC,IAAI,IAAI,GAAG;EAC1B,IAAIC,KAAK,GAAGC,kBAAkB,CAACL,GAAG,CAAC;EACnC,IAAIM,GAAG,GAAG,EAAE,CAACC,MAAM,CAACR,IAAI,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACH,KAAK,CAAC;EAC5C,IAAIF,GAAG,CAACM,MAAM,GAAG,CAAC,EAAE;IAClB,IAAIA,MAAM,GAAGN,GAAG,CAACM,MAAM,GAAG,CAAC;IAC3B,IAAIC,MAAM,CAACC,KAAK,CAACF,MAAM,CAAC,EAAE,MAAM,IAAIG,KAAK,CAAC,2BAA2B,CAAC;IACtEL,GAAG,IAAI,YAAY,CAACC,MAAM,CAACK,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC,CAAC;EAChD;EACA,IAAIN,GAAG,CAACY,MAAM,EAAE;IACd,IAAI,CAACjB,kBAAkB,CAACkB,IAAI,CAACb,GAAG,CAACY,MAAM,CAAC,EAAE;MACxC,MAAM,IAAIE,SAAS,CAAC,0BAA0B,CAAC;IACjD;IACAV,GAAG,IAAI,WAAW,CAACC,MAAM,CAACL,GAAG,CAACY,MAAM,CAAC;EACvC;EACA,IAAIZ,GAAG,CAACC,IAAI,EAAE;IACZ,IAAI,CAACN,kBAAkB,CAACkB,IAAI,CAACb,GAAG,CAACC,IAAI,CAAC,EAAE;MACtC,MAAM,IAAIa,SAAS,CAAC,wBAAwB,CAAC;IAC/C;IACAV,GAAG,IAAI,SAAS,CAACC,MAAM,CAACL,GAAG,CAACC,IAAI,CAAC;EACnC;EACA,IAAID,GAAG,CAACe,OAAO,EAAE;IACf,IAAI,OAAOf,GAAG,CAACe,OAAO,CAACC,WAAW,KAAK,UAAU,EAAE;MACjD,MAAM,IAAIF,SAAS,CAAC,2BAA2B,CAAC;IAClD;IACAV,GAAG,IAAI,YAAY,CAACC,MAAM,CAACL,GAAG,CAACe,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC;EACvD;EACA,IAAIhB,GAAG,CAACiB,QAAQ,EAAEb,GAAG,IAAI,YAAY;EACrC,IAAIJ,GAAG,CAACkB,MAAM,EAAEd,GAAG,IAAI,UAAU;EACjC,IAAIJ,GAAG,CAACmB,QAAQ,EAAE;IAChB,IAAIA,QAAQ,GAAG,OAAOnB,GAAG,CAACmB,QAAQ,KAAK,QAAQ,GAAGnB,GAAG,CAACmB,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAGpB,GAAG,CAACmB,QAAQ;IAC3F,QAAQA,QAAQ;MACd,KAAK,IAAI;QACPf,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,KAAK;QACRA,GAAG,IAAI,gBAAgB;QACvB;MACF,KAAK,QAAQ;QACXA,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,MAAM;QACTA,GAAG,IAAI,iBAAiB;QACxB;MACF;QACE,MAAM,IAAIU,SAAS,CAAC,4BAA4B,CAAC;IACrD;EACF;EACA,OAAOV,GAAG;AACZ,CAAC;AACD,IAAIiB,MAAM,GAAG;EACXC,MAAM,EAAE,SAASA,MAAMA,CAACzB,IAAI,EAAEK,KAAK,EAAEqB,OAAO,EAAEX,MAAM,EAAE;IACpD,IAAIY,aAAa,GAAGjC,SAAS,CAACkC,MAAM,GAAG,CAAC,IAAIlC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG;MACtFU,IAAI,EAAE,GAAG;MACTkB,QAAQ,EAAE;IACZ,CAAC;IACD,IAAII,OAAO,EAAE;MACXC,aAAa,CAACT,OAAO,GAAG,IAAIW,IAAI,CAAC,CAAC;MAClCF,aAAa,CAACT,OAAO,CAACY,OAAO,CAACH,aAAa,CAACT,OAAO,CAACa,OAAO,CAAC,CAAC,GAAGL,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC;IACtF;IACA,IAAIX,MAAM,EAAEY,aAAa,CAACZ,MAAM,GAAGA,MAAM;IACzCiB,QAAQ,CAACR,MAAM,GAAGzB,eAAe,CAACC,IAAI,EAAEM,kBAAkB,CAACD,KAAK,CAAC,EAAEsB,aAAa,CAAC;EACnF,CAAC;EACDM,IAAI,EAAE,SAASA,IAAIA,CAACjC,IAAI,EAAE;IACxB,IAAIkC,MAAM,GAAG,EAAE,CAAC1B,MAAM,CAACR,IAAI,EAAE,GAAG,CAAC;IACjC,IAAImC,EAAE,GAAGH,QAAQ,CAACR,MAAM,CAACY,KAAK,CAAC,GAAG,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,EAAE,CAACP,MAAM,EAAES,CAAC,EAAE,EAAE;MAClC,IAAIC,CAAC,GAAGH,EAAE,CAACE,CAAC,CAAC;MACb,OAAOC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAED,CAAC,GAAGA,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEF,CAAC,CAACV,MAAM,CAAC;MACxD,IAAIU,CAAC,CAACG,OAAO,CAACP,MAAM,CAAC,KAAK,CAAC,EAAE,OAAOI,CAAC,CAACE,SAAS,CAACN,MAAM,CAACN,MAAM,EAAEU,CAAC,CAACV,MAAM,CAAC;IAC1E;IACA,OAAO,IAAI;EACb,CAAC;EACDc,MAAM,EAAE,SAASA,MAAMA,CAAC1C,IAAI,EAAE;IAC5B,IAAI,CAACyB,MAAM,CAACzB,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EAC3B;AACF,CAAC;AACD,IAAI2C,QAAQ,GAAG;EACb3C,IAAI,EAAE,QAAQ;EACd4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAI3C,OAAO,CAAC4C,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MAC3D,IAAIM,CAAC,GAAGd,MAAM,CAACS,IAAI,CAAC/B,OAAO,CAAC4C,YAAY,CAAC;MACzC,IAAIR,CAAC,EAAEO,KAAK,GAAGP,CAAC;IAClB;IACA,OAAOO,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE9C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAAC4C,YAAY,IAAI,OAAOd,QAAQ,KAAK,WAAW,EAAE;MAC3DR,MAAM,CAACC,MAAM,CAACvB,OAAO,CAAC4C,YAAY,EAAEE,GAAG,EAAE9C,OAAO,CAAC+C,aAAa,EAAE/C,OAAO,CAACgD,YAAY,EAAEhD,OAAO,CAACyB,aAAa,CAAC;IAC9G;EACF;AACF,CAAC;AAED,IAAIwB,WAAW,GAAG;EAChBnD,IAAI,EAAE,aAAa;EACnB4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAE;MACjC,IAAIC,MAAM,GAAGD,MAAM,CAACE,QAAQ,CAACD,MAAM;MACnC,IAAI,CAACD,MAAM,CAACE,QAAQ,CAACD,MAAM,IAAID,MAAM,CAACE,QAAQ,CAACC,IAAI,IAAIH,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACd,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7FY,MAAM,GAAGD,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACf,SAAS,CAACY,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACd,OAAO,CAAC,GAAG,CAAC,CAAC;MAC5E;MACA,IAAIe,KAAK,GAAGH,MAAM,CAACb,SAAS,CAAC,CAAC,CAAC;MAC/B,IAAIiB,MAAM,GAAGD,KAAK,CAACpB,KAAK,CAAC,GAAG,CAAC;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,CAAC7B,MAAM,EAAES,CAAC,EAAE,EAAE;QACtC,IAAIqB,GAAG,GAAGD,MAAM,CAACpB,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,CAAC;QAChC,IAAIiB,GAAG,GAAG,CAAC,EAAE;UACX,IAAIC,GAAG,GAAGF,MAAM,CAACpB,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEkB,GAAG,CAAC;UACrC,IAAIC,GAAG,KAAKzD,OAAO,CAAC0D,iBAAiB,EAAE;YACrCf,KAAK,GAAGY,MAAM,CAACpB,CAAC,CAAC,CAACG,SAAS,CAACkB,GAAG,GAAG,CAAC,CAAC;UACtC;QACF;MACF;IACF;IACA,OAAOb,KAAK;EACd;AACF,CAAC;AAED,IAAIgB,sBAAsB,GAAG,IAAI;AACjC,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;EAC3D,IAAID,sBAAsB,KAAK,IAAI,EAAE,OAAOA,sBAAsB;EAClE,IAAI;IACFA,sBAAsB,GAAGT,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACW,YAAY,KAAK,IAAI;IAC/E,IAAIC,OAAO,GAAG,uBAAuB;IACrCZ,MAAM,CAACW,YAAY,CAACE,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC3CZ,MAAM,CAACW,YAAY,CAACG,UAAU,CAACF,OAAO,CAAC;EACzC,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVN,sBAAsB,GAAG,KAAK;EAChC;EACA,OAAOA,sBAAsB;AAC/B,CAAC;AACD,IAAIE,YAAY,GAAG;EACjB/D,IAAI,EAAE,cAAc;EACpB4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAI3C,OAAO,CAACkE,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACzD,IAAId,GAAG,GAAGI,MAAM,CAACW,YAAY,CAACM,OAAO,CAACnE,OAAO,CAACkE,kBAAkB,CAAC;MACjE,IAAIpB,GAAG,EAAEH,KAAK,GAAGG,GAAG;IACtB;IACA,OAAOH,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE9C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAACkE,kBAAkB,IAAIN,qBAAqB,CAAC,CAAC,EAAE;MACzDV,MAAM,CAACW,YAAY,CAACE,OAAO,CAAC/D,OAAO,CAACkE,kBAAkB,EAAEpB,GAAG,CAAC;IAC9D;EACF;AACF,CAAC;AAED,IAAIsB,wBAAwB,GAAG,IAAI;AACnC,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;EAC/D,IAAID,wBAAwB,KAAK,IAAI,EAAE,OAAOA,wBAAwB;EACtE,IAAI;IACFA,wBAAwB,GAAGlB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACoB,cAAc,KAAK,IAAI;IACnF,IAAIR,OAAO,GAAG,uBAAuB;IACrCZ,MAAM,CAACoB,cAAc,CAACP,OAAO,CAACD,OAAO,EAAE,KAAK,CAAC;IAC7CZ,MAAM,CAACoB,cAAc,CAACN,UAAU,CAACF,OAAO,CAAC;EAC3C,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVG,wBAAwB,GAAG,KAAK;EAClC;EACA,OAAOA,wBAAwB;AACjC,CAAC;AACD,IAAIE,cAAc,GAAG;EACnBxE,IAAI,EAAE,gBAAgB;EACtB4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAI3C,OAAO,CAACuE,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MAC7D,IAAIvB,GAAG,GAAGI,MAAM,CAACoB,cAAc,CAACH,OAAO,CAACnE,OAAO,CAACuE,oBAAoB,CAAC;MACrE,IAAIzB,GAAG,EAAEH,KAAK,GAAGG,GAAG;IACtB;IACA,OAAOH,KAAK;EACd,CAAC;EACDE,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,GAAG,EAAE9C,OAAO,EAAE;IAC1D,IAAIA,OAAO,CAACuE,oBAAoB,IAAIF,uBAAuB,CAAC,CAAC,EAAE;MAC7DnB,MAAM,CAACoB,cAAc,CAACP,OAAO,CAAC/D,OAAO,CAACuE,oBAAoB,EAAEzB,GAAG,CAAC;IAClE;EACF;AACF,CAAC;AAED,IAAI0B,WAAW,GAAG;EAChB1E,IAAI,EAAE,WAAW;EACjB4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK,GAAG,EAAE;IACd,IAAI,OAAO8B,SAAS,KAAK,WAAW,EAAE;MACpC,IAAIA,SAAS,CAACC,SAAS,EAAE;QACvB;QACA,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,SAAS,CAACC,SAAS,CAAChD,MAAM,EAAES,CAAC,EAAE,EAAE;UACnDQ,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACC,SAAS,CAACvC,CAAC,CAAC,CAAC;QACpC;MACF;MACA,IAAIsC,SAAS,CAACG,YAAY,EAAE;QAC1BjC,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACG,YAAY,CAAC;MACpC;MACA,IAAIH,SAAS,CAACI,QAAQ,EAAE;QACtBlC,KAAK,CAACgC,IAAI,CAACF,SAAS,CAACI,QAAQ,CAAC;MAChC;IACF;IACA,OAAOlC,KAAK,CAACjB,MAAM,GAAG,CAAC,GAAGiB,KAAK,GAAGhD,SAAS;EAC7C;AACF,CAAC;AAED,IAAImF,OAAO,GAAG;EACZhF,IAAI,EAAE,SAAS;EACf4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAImC,OAAO,GAAG9E,OAAO,CAAC8E,OAAO,KAAK,OAAOhD,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACiD,eAAe,GAAG,IAAI,CAAC;IACpG,IAAID,OAAO,IAAI,OAAOA,OAAO,CAACE,YAAY,KAAK,UAAU,EAAE;MACzDrC,KAAK,GAAGmC,OAAO,CAACE,YAAY,CAAC,MAAM,CAAC;IACtC;IACA,OAAOrC,KAAK;EACd;AACF,CAAC;AAED,IAAIzC,IAAI,GAAG;EACTJ,IAAI,EAAE,MAAM;EACZ4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B,IAAI2C,KAAK;IACT,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAE;MACjC,IAAI2B,QAAQ,GAAG3B,MAAM,CAACE,QAAQ,CAAC6B,QAAQ,CAACC,KAAK,CAAC,iBAAiB,CAAC;MAChE,IAAIL,QAAQ,YAAYM,KAAK,EAAE;QAC7B,IAAI,OAAOnF,OAAO,CAACoF,mBAAmB,KAAK,QAAQ,EAAE;UACnD,IAAI,OAAOP,QAAQ,CAAC7E,OAAO,CAACoF,mBAAmB,CAAC,KAAK,QAAQ,EAAE;YAC7D,OAAOzF,SAAS;UAClB;UACAgD,KAAK,GAAGkC,QAAQ,CAAC7E,OAAO,CAACoF,mBAAmB,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QAChE,CAAC,MAAM;UACL1C,KAAK,GAAGkC,QAAQ,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;QACtC;MACF;IACF;IACA,OAAO1C,KAAK;EACd;AACF,CAAC;AAED,IAAI2C,SAAS,GAAG;EACdxF,IAAI,EAAE,WAAW;EACjB4C,MAAM,EAAE,SAASA,MAAMA,CAAC1C,OAAO,EAAE;IAC/B;IACA,IAAIuF,wBAAwB,GAAG,OAAOvF,OAAO,CAACuF,wBAAwB,KAAK,QAAQ,GAAGvF,OAAO,CAACuF,wBAAwB,GAAG,CAAC,GAAG,CAAC;IAC9H;IACA;IACA;IACA,IAAIV,QAAQ,GAAG,OAAO3B,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACE,QAAQ,IAAIF,MAAM,CAACE,QAAQ,CAACoC,QAAQ,IAAItC,MAAM,CAACE,QAAQ,CAACoC,QAAQ,CAACN,KAAK,CAAC,wDAAwD,CAAC;;IAEvL;IACA,IAAI,CAACL,QAAQ,EAAE,OAAOlF,SAAS;IAC/B;IACA,OAAOkF,QAAQ,CAACU,wBAAwB,CAAC;EAC3C;AACF,CAAC;;AAED;AACA,IAAIE,UAAU,GAAG,KAAK;AACtB,IAAI;EACF;EACA3D,QAAQ,CAACR,MAAM;EACfmE,UAAU,GAAG,IAAI;EACjB;AACF,CAAC,CAAC,OAAOxB,CAAC,EAAE,CAAC;AACb,IAAIyB,KAAK,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,CAAC;AAC/F,IAAI,CAACD,UAAU,EAAEC,KAAK,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,SAASC,WAAWA,CAAA,EAAG;EACrB,OAAO;IACLF,KAAK,EAAEA,KAAK;IACZhC,iBAAiB,EAAE,KAAK;IACxBd,YAAY,EAAE,SAAS;IACvBsB,kBAAkB,EAAE,YAAY;IAChCK,oBAAoB,EAAE,YAAY;IAClC;IACAsB,MAAM,EAAE,CAAC,cAAc,CAAC;IACxBC,eAAe,EAAE,CAAC,QAAQ,CAAC;IAC3B;IACA;;IAEAC,uBAAuB,EAAE,SAASA,uBAAuBA,CAACC,CAAC,EAAE;MAC3D,OAAOA,CAAC;IACV;EACF,CAAC;AACH;AACA,IAAIC,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAOA,CAACC,QAAQ,EAAE;IACzB,IAAIlG,OAAO,GAAGR,SAAS,CAACkC,MAAM,GAAG,CAAC,IAAIlC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpFT,eAAe,CAAC,IAAI,EAAEkH,OAAO,CAAC;IAC9B,IAAI,CAACE,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,IAAI,CAACH,QAAQ,EAAElG,OAAO,CAAC;EAC9B;EACA,OAAOhB,YAAY,CAACiH,OAAO,EAAE,CAAC;IAC5BxC,GAAG,EAAE,MAAM;IACXtD,KAAK,EAAE,SAASkG,IAAIA,CAACH,QAAQ,EAAE;MAC7B,IAAIlG,OAAO,GAAGR,SAAS,CAACkC,MAAM,GAAG,CAAC,IAAIlC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACpF,IAAI8G,WAAW,GAAG9G,SAAS,CAACkC,MAAM,GAAG,CAAC,IAAIlC,SAAS,CAAC,CAAC,CAAC,KAAKG,SAAS,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACxF,IAAI,CAAC0G,QAAQ,GAAGA,QAAQ,IAAI;QAC1BK,aAAa,EAAE,CAAC;MAClB,CAAC,CAAC,CAAC;MACH,IAAI,CAACvG,OAAO,GAAGX,QAAQ,CAACW,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC,EAAE4F,WAAW,CAAC,CAAC,CAAC;MACnE,IAAI,OAAO,IAAI,CAAC5F,OAAO,CAAC+F,uBAAuB,KAAK,QAAQ,IAAI,IAAI,CAAC/F,OAAO,CAAC+F,uBAAuB,CAACxD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC1H,IAAI,CAACvC,OAAO,CAAC+F,uBAAuB,GAAG,UAAUC,CAAC,EAAE;UAClD,OAAOA,CAAC,CAACX,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAC5B,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACrF,OAAO,CAACwG,kBAAkB,EAAE,IAAI,CAACxG,OAAO,CAACoF,mBAAmB,GAAG,IAAI,CAACpF,OAAO,CAACwG,kBAAkB;MACvG,IAAI,CAACF,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACG,WAAW,CAAChE,QAAQ,CAAC;MAC1B,IAAI,CAACgE,WAAW,CAACxD,WAAW,CAAC;MAC7B,IAAI,CAACwD,WAAW,CAAC5C,YAAY,CAAC;MAC9B,IAAI,CAAC4C,WAAW,CAACnC,cAAc,CAAC;MAChC,IAAI,CAACmC,WAAW,CAACjC,WAAW,CAAC;MAC7B,IAAI,CAACiC,WAAW,CAAC3B,OAAO,CAAC;MACzB,IAAI,CAAC2B,WAAW,CAACvG,IAAI,CAAC;MACtB,IAAI,CAACuG,WAAW,CAACnB,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,aAAa;IAClBtD,KAAK,EAAE,SAASsG,WAAWA,CAACC,QAAQ,EAAE;MACpC,IAAI,CAACN,SAAS,CAACM,QAAQ,CAAC5G,IAAI,CAAC,GAAG4G,QAAQ;MACxC,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjD,GAAG,EAAE,QAAQ;IACbtD,KAAK,EAAE,SAASwG,MAAMA,CAACC,cAAc,EAAE;MACrC,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI,CAACD,cAAc,EAAEA,cAAc,GAAG,IAAI,CAAC5G,OAAO,CAAC0F,KAAK;MACxD,IAAIoB,QAAQ,GAAG,EAAE;MACjBF,cAAc,CAACzH,OAAO,CAAC,UAAU4H,YAAY,EAAE;QAC7C,IAAIF,KAAK,CAACT,SAAS,CAACW,YAAY,CAAC,EAAE;UACjC,IAAIrE,MAAM,GAAGmE,KAAK,CAACT,SAAS,CAACW,YAAY,CAAC,CAACrE,MAAM,CAACmE,KAAK,CAAC7G,OAAO,CAAC;UAChE,IAAI0C,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAEA,MAAM,GAAG,CAACA,MAAM,CAAC;UAC3D,IAAIA,MAAM,EAAEoE,QAAQ,GAAGA,QAAQ,CAACxG,MAAM,CAACoC,MAAM,CAAC;QAChD;MACF,CAAC,CAAC;MACFoE,QAAQ,GAAGA,QAAQ,CAACE,GAAG,CAAC,UAAUC,CAAC,EAAE;QACnC,OAAOJ,KAAK,CAAC7G,OAAO,CAAC+F,uBAAuB,CAACkB,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,IAAI,CAACf,QAAQ,CAACK,aAAa,CAACW,qBAAqB,EAAE,OAAOJ,QAAQ,CAAC,CAAC;MACxE,OAAOA,QAAQ,CAACpF,MAAM,GAAG,CAAC,GAAGoF,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACnD;EACF,CAAC,EAAE;IACDrD,GAAG,EAAE,mBAAmB;IACxBtD,KAAK,EAAE,SAAS0C,iBAAiBA,CAACC,GAAG,EAAE+C,MAAM,EAAE;MAC7C,IAAIsB,MAAM,GAAG,IAAI;MACjB,IAAI,CAACtB,MAAM,EAAEA,MAAM,GAAG,IAAI,CAAC7F,OAAO,CAAC6F,MAAM;MACzC,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,IAAI,CAAC7F,OAAO,CAAC8F,eAAe,IAAI,IAAI,CAAC9F,OAAO,CAAC8F,eAAe,CAACvD,OAAO,CAACO,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACpF+C,MAAM,CAAC1G,OAAO,CAAC,UAAUiI,SAAS,EAAE;QAClC,IAAID,MAAM,CAACf,SAAS,CAACgB,SAAS,CAAC,EAAED,MAAM,CAACf,SAAS,CAACgB,SAAS,CAAC,CAACvE,iBAAiB,CAACC,GAAG,EAAEqE,MAAM,CAACnH,OAAO,CAAC;MACrG,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHiG,OAAO,CAACE,IAAI,GAAG,kBAAkB;AAEjC,SAASF,OAAO,IAAIoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
const express = require('express');
const router = express.Router();

// Controllers
const customerController = require('../controllers/customerController');

// Middleware
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');

/**
 * @route   GET /api/customers/select
 * @desc    Get customers for select dropdown
 * @access  Private (requires equipment.read permission)
 */
router.get('/select', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  customerController.getCustomersForSelect
);

/**
 * @route   GET /api/customers
 * @desc    Get all customers
 * @access  Private (requires equipment.read permission)
 */
router.get('/', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  customerController.getAllCustomers
);

/**
 * @route   GET /api/customers/:id
 * @desc    Get customer by ID
 * @access  Private (requires equipment.read permission)
 */
router.get('/:id', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  customerController.getCustomerById
);

/**
 * @route   POST /api/customers
 * @desc    Create new customer
 * @access  Private (requires equipment.create permission)
 */
router.post('/', 
  authenticateToken, 
  requirePermission('equipment.create'), 
  customerController.createCustomer
);

/**
 * @route   PUT /api/customers/:id
 * @desc    Update customer
 * @access  Private (requires equipment.update permission)
 */
router.put('/:id', 
  authenticateToken, 
  requirePermission('equipment.update'), 
  customerController.updateCustomer
);

/**
 * @route   DELETE /api/customers/:id
 * @desc    Delete customer
 * @access  Private (requires equipment.delete permission)
 */
router.delete('/:id', 
  authenticateToken, 
  requirePermission('equipment.delete'), 
  customerController.deleteCustomer
);

module.exports = router;

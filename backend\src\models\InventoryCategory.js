const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InventoryCategory = sequelize.define('InventoryCategory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    nameAr: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'name_ar',
      validate: {
        len: [2, 100]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parentId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'parent_id',
      references: {
        model: 'inventory_categories',
        key: 'id'
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'inventory_categories',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['parent_id']
      },
      {
        fields: ['name']
      }
    ]
  });

  // Instance methods
  InventoryCategory.prototype.getFullPath = async function() {
    let path = [this.name];
    let current = this;
    
    while (current.parentId) {
      current = await InventoryCategory.findByPk(current.parentId);
      if (current) {
        path.unshift(current.name);
      } else {
        break;
      }
    }
    
    return path.join(' > ');
  };

  // Associations
  InventoryCategory.associate = function(models) {
    // Self-referencing association for parent-child relationship
    InventoryCategory.belongsTo(InventoryCategory, {
      foreignKey: 'parent_id',
      as: 'parent'
    });

    InventoryCategory.hasMany(InventoryCategory, {
      foreignKey: 'parent_id',
      as: 'children'
    });

    // InventoryCategory has many InventoryItems
    if (models.InventoryItem) {
      InventoryCategory.hasMany(models.InventoryItem, {
        foreignKey: 'category_id',
        as: 'items'
      });
    }
  };

  return InventoryCategory;
};

{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Fady\\\\frontend\\\\src\\\\pages\\\\Dashboard\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport { CogIcon, ArchiveBoxIcon, WrenchScrewdriverIcon, DocumentTextIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { selectUser } from '../../store/slices/authSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const user = useSelector(selectUser);\n\n  // بيانات وهمية للإحصائيات\n  const stats = [{\n    name: 'إجمالي المعدات',\n    value: '24',\n    change: '+2',\n    changeType: 'increase',\n    icon: CogIcon,\n    color: 'bg-blue-500'\n  }, {\n    name: 'أصناف المخزون',\n    value: '156',\n    change: '+12',\n    changeType: 'increase',\n    icon: ArchiveBoxIcon,\n    color: 'bg-green-500'\n  }, {\n    name: 'أوامر الصيانة النشطة',\n    value: '8',\n    change: '-3',\n    changeType: 'decrease',\n    icon: WrenchScrewdriverIcon,\n    color: 'bg-yellow-500'\n  }, {\n    name: 'الفواتير المعلقة',\n    value: '5',\n    change: '+1',\n    changeType: 'increase',\n    icon: DocumentTextIcon,\n    color: 'bg-red-500'\n  }];\n  const recentActivities = [{\n    id: 1,\n    type: 'maintenance',\n    title: 'تم إنشاء أمر صيانة جديد',\n    description: 'صيانة دورية للحفارة CAT-001',\n    time: 'منذ ساعتين',\n    user: 'أحمد محمد'\n  }, {\n    id: 2,\n    type: 'inventory',\n    title: 'تنبيه مخزون منخفض',\n    description: 'فلاتر الزيت - الكمية المتبقية: 5 قطع',\n    time: 'منذ 3 ساعات',\n    user: 'النظام'\n  }, {\n    id: 3,\n    type: 'invoice',\n    title: 'فاتورة جديدة',\n    description: 'فاتورة صيانة للعميل شركة البناء المتطور',\n    time: 'منذ 4 ساعات',\n    user: 'سارة أحمد'\n  }, {\n    id: 4,\n    type: 'equipment',\n    title: 'إضافة معدة جديدة',\n    description: 'تم تسجيل رافعة شوكية جديدة FL-005',\n    time: 'أمس',\n    user: 'محمد علي'\n  }];\n  const alerts = [{\n    id: 1,\n    type: 'warning',\n    title: 'صيانة مستحقة',\n    message: 'الحفارة CAT-002 تحتاج صيانة دورية خلال 3 أيام',\n    priority: 'متوسط'\n  }, {\n    id: 2,\n    type: 'error',\n    title: 'مخزون منخفض',\n    message: 'زيت المحرك - الكمية المتبقية: 2 لتر فقط',\n    priority: 'عالي'\n  }, {\n    id: 3,\n    type: 'info',\n    title: 'فاتورة مستحقة',\n    message: 'فاتورة العميل ABC Company مستحقة خلال 5 أيام',\n    priority: 'منخفض'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-2\",\n        children: [\"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B\\u060C \", user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-indigo-100\",\n        children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0648\\u0631\\u0634\\u0629 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0627\\u062A \\u0627\\u0644\\u062B\\u0642\\u064A\\u0644\\u0629. \\u0625\\u0644\\u064A\\u0643 \\u0646\\u0638\\u0631\\u0629 \\u0633\\u0631\\u064A\\u0639\\u0629 \\u0639\\u0644\\u0649 \\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0623\\u0646\\u0634\\u0637\\u0629.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: stats.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${stat.color} p-3 rounded-lg`,\n            children: /*#__PURE__*/_jsxDEV(stat.icon, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: stat.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `mr-2 text-sm ${stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`,\n                children: stat.change\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)\n      }, stat.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\u0627\\u0644\\u0623\\u0646\\u0634\\u0637\\u0629 \\u0627\\u0644\\u0623\\u062E\\u064A\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: recentActivities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3 space-x-reverse\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-2 w-2 bg-indigo-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900\",\n                children: activity.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: activity.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-1 text-xs text-gray-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: activity.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mx-2\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: activity.user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, activity.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"\\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: alerts.map(alert => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg border ${alert.type === 'error' ? 'bg-red-50 border-red-200' : alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-blue-50 border-blue-200'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                  className: `h-5 w-5 ${alert.type === 'error' ? 'text-red-400' : alert.type === 'warning' ? 'text-yellow-400' : 'text-blue-400'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mr-3 flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: alert.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 mt-1\",\n                  children: alert.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${alert.priority === 'عالي' ? 'bg-red-100 text-red-800' : alert.priority === 'متوسط' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}`,\n                  children: [\"\\u0623\\u0648\\u0644\\u0648\\u064A\\u0629 \", alert.priority]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)\n          }, alert.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"ODc3cjw/vvoWaLFqHWcEGODyOfo=\", false, function () {\n  return [useSelector];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useSelector", "CogIcon", "ArchiveBoxIcon", "WrenchScrewdriverIcon", "DocumentTextIcon", "ExclamationTriangleIcon", "selectUser", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "name", "value", "change", "changeType", "icon", "color", "recentActivities", "id", "type", "title", "description", "time", "alerts", "message", "priority", "className", "children", "firstName", "lastName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stat", "activity", "alert", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/pages/Dashboard/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport {\n  CogIcon,\n  ArchiveBoxIcon,\n  WrenchScrewdriverIcon,\n  DocumentTextIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\nimport { selectUser } from '../../store/slices/authSlice';\n\nconst Dashboard = () => {\n  const user = useSelector(selectUser);\n\n  // بيانات وهمية للإحصائيات\n  const stats = [\n    {\n      name: 'إجمالي المعدات',\n      value: '24',\n      change: '+2',\n      changeType: 'increase',\n      icon: CogIcon,\n      color: 'bg-blue-500',\n    },\n    {\n      name: 'أصناف المخزون',\n      value: '156',\n      change: '+12',\n      changeType: 'increase',\n      icon: ArchiveBoxIcon,\n      color: 'bg-green-500',\n    },\n    {\n      name: 'أوامر الصيانة النشطة',\n      value: '8',\n      change: '-3',\n      changeType: 'decrease',\n      icon: WrenchScrewdriverIcon,\n      color: 'bg-yellow-500',\n    },\n    {\n      name: 'الفواتير المعلقة',\n      value: '5',\n      change: '+1',\n      changeType: 'increase',\n      icon: DocumentTextIcon,\n      color: 'bg-red-500',\n    },\n  ];\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'maintenance',\n      title: 'تم إنشاء أمر صيانة جديد',\n      description: 'صيانة دورية للحفارة CAT-001',\n      time: 'منذ ساعتين',\n      user: 'أحمد محمد',\n    },\n    {\n      id: 2,\n      type: 'inventory',\n      title: 'تنبيه مخزون منخفض',\n      description: 'فلاتر الزيت - الكمية المتبقية: 5 قطع',\n      time: 'منذ 3 ساعات',\n      user: 'النظام',\n    },\n    {\n      id: 3,\n      type: 'invoice',\n      title: 'فاتورة جديدة',\n      description: 'فاتورة صيانة للعميل شركة البناء المتطور',\n      time: 'منذ 4 ساعات',\n      user: 'سارة أحمد',\n    },\n    {\n      id: 4,\n      type: 'equipment',\n      title: 'إضافة معدة جديدة',\n      description: 'تم تسجيل رافعة شوكية جديدة FL-005',\n      time: 'أمس',\n      user: 'محمد علي',\n    },\n  ];\n\n  const alerts = [\n    {\n      id: 1,\n      type: 'warning',\n      title: 'صيانة مستحقة',\n      message: 'الحفارة CAT-002 تحتاج صيانة دورية خلال 3 أيام',\n      priority: 'متوسط',\n    },\n    {\n      id: 2,\n      type: 'error',\n      title: 'مخزون منخفض',\n      message: 'زيت المحرك - الكمية المتبقية: 2 لتر فقط',\n      priority: 'عالي',\n    },\n    {\n      id: 3,\n      type: 'info',\n      title: 'فاتورة مستحقة',\n      message: 'فاتورة العميل ABC Company مستحقة خلال 5 أيام',\n      priority: 'منخفض',\n    },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">\n          مرحباً، {user?.firstName} {user?.lastName}\n        </h1>\n        <p className=\"text-indigo-100\">\n          مرحباً بك في نظام إدارة ورشة المعدات الثقيلة. إليك نظرة سريعة على أحدث الأنشطة.\n        </p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {stats.map((stat) => (\n          <div key={stat.name} className=\"card\">\n            <div className=\"flex items-center\">\n              <div className={`${stat.color} p-3 rounded-lg`}>\n                <stat.icon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                <div className=\"flex items-center\">\n                  <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                  <span className={`mr-2 text-sm ${\n                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {stat.change}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Activities */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">الأنشطة الأخيرة</h3>\n          </div>\n          <div className=\"space-y-4\">\n            {recentActivities.map((activity) => (\n              <div key={activity.id} className=\"flex items-start space-x-3 space-x-reverse\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center\">\n                    <div className=\"h-2 w-2 bg-indigo-500 rounded-full\"></div>\n                  </div>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                  <p className=\"text-sm text-gray-500\">{activity.description}</p>\n                  <div className=\"flex items-center mt-1 text-xs text-gray-400\">\n                    <span>{activity.time}</span>\n                    <span className=\"mx-2\">•</span>\n                    <span>{activity.user}</span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Alerts */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">التنبيهات</h3>\n          </div>\n          <div className=\"space-y-4\">\n            {alerts.map((alert) => (\n              <div key={alert.id} className={`p-4 rounded-lg border ${\n                alert.type === 'error' ? 'bg-red-50 border-red-200' :\n                alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :\n                'bg-blue-50 border-blue-200'\n              }`}>\n                <div className=\"flex items-start\">\n                  <div className=\"flex-shrink-0\">\n                    <ExclamationTriangleIcon className={`h-5 w-5 ${\n                      alert.type === 'error' ? 'text-red-400' :\n                      alert.type === 'warning' ? 'text-yellow-400' :\n                      'text-blue-400'\n                    }`} />\n                  </div>\n                  <div className=\"mr-3 flex-1\">\n                    <h4 className=\"text-sm font-medium text-gray-900\">{alert.title}</h4>\n                    <p className=\"text-sm text-gray-600 mt-1\">{alert.message}</p>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${\n                      alert.priority === 'عالي' ? 'bg-red-100 text-red-800' :\n                      alert.priority === 'متوسط' ? 'bg-yellow-100 text-yellow-800' :\n                      'bg-blue-100 text-blue-800'\n                    }`}>\n                      أولوية {alert.priority}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,OAAO,EACPC,cAAc,EACdC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAuB,QAClB,6BAA6B;AACpC,SAASC,UAAU,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,IAAI,GAAGX,WAAW,CAACM,UAAU,CAAC;;EAEpC;EACA,MAAMM,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEhB,OAAO;IACbiB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEf,cAAc;IACpBgB,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEd,qBAAqB;IAC3Be,KAAK,EAAE;EACT,CAAC,EACD;IACEL,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEb,gBAAgB;IACtBc,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE,YAAY;IAClBb,IAAI,EAAE;EACR,CAAC,EACD;IACES,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,sCAAsC;IACnDC,IAAI,EAAE,aAAa;IACnBb,IAAI,EAAE;EACR,CAAC,EACD;IACES,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE,aAAa;IACnBb,IAAI,EAAE;EACR,CAAC,EACD;IACES,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE,KAAK;IACXb,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMc,MAAM,GAAG,CACb;IACEL,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,cAAc;IACrBI,OAAO,EAAE,+CAA+C;IACxDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBI,OAAO,EAAE,yCAAyC;IAClDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,eAAe;IACtBI,OAAO,EAAE,8CAA8C;IACvDC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBrB,OAAA;MAAKoB,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBACjGrB,OAAA;QAAIoB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GAAC,6CAC9B,EAAClB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS,EAAC,GAAC,EAACnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACL3B,OAAA;QAAGoB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAE/B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN3B,OAAA;MAAKoB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEjB,KAAK,CAACwB,GAAG,CAAEC,IAAI,iBACd7B,OAAA;QAAqBoB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnCrB,OAAA;UAAKoB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrB,OAAA;YAAKoB,SAAS,EAAE,GAAGS,IAAI,CAACnB,KAAK,iBAAkB;YAAAW,QAAA,eAC7CrB,OAAA,CAAC6B,IAAI,CAACpB,IAAI;cAACW,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN3B,OAAA;YAAKoB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBrB,OAAA;cAAGoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEQ,IAAI,CAACxB;YAAI;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChE3B,OAAA;cAAKoB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCrB,OAAA;gBAAGoB,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAEQ,IAAI,CAACvB;cAAK;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE3B,OAAA;gBAAMoB,SAAS,EAAE,gBACfS,IAAI,CAACrB,UAAU,KAAK,UAAU,GAAG,gBAAgB,GAAG,cAAc,EACjE;gBAAAa,QAAA,EACAQ,IAAI,CAACtB;cAAM;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAhBEE,IAAI,CAACxB,IAAI;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiBd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN3B,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrB,OAAA;QAAKoB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrB,OAAA;YAAIoB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN3B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBV,gBAAgB,CAACiB,GAAG,CAAEE,QAAQ,iBAC7B9B,OAAA;YAAuBoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3ErB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA;gBAAKoB,SAAS,EAAC,mEAAmE;gBAAAC,QAAA,eAChFrB,OAAA;kBAAKoB,SAAS,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3B,OAAA;cAAKoB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrB,OAAA;gBAAGoB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAES,QAAQ,CAAChB;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrE3B,OAAA;gBAAGoB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAES,QAAQ,CAACf;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/D3B,OAAA;gBAAKoB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,gBAC3DrB,OAAA;kBAAAqB,QAAA,EAAOS,QAAQ,CAACd;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5B3B,OAAA;kBAAMoB,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/B3B,OAAA;kBAAAqB,QAAA,EAAOS,QAAQ,CAAC3B;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAdEG,QAAQ,CAAClB,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAehB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKoB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrB,OAAA;YAAIoB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACN3B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBJ,MAAM,CAACW,GAAG,CAAEG,KAAK,iBAChB/B,OAAA;YAAoBoB,SAAS,EAAE,yBAC7BW,KAAK,CAAClB,IAAI,KAAK,OAAO,GAAG,0BAA0B,GACnDkB,KAAK,CAAClB,IAAI,KAAK,SAAS,GAAG,gCAAgC,GAC3D,4BAA4B,EAC3B;YAAAQ,QAAA,eACDrB,OAAA;cAAKoB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BrB,OAAA;gBAAKoB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BrB,OAAA,CAACH,uBAAuB;kBAACuB,SAAS,EAAE,WAClCW,KAAK,CAAClB,IAAI,KAAK,OAAO,GAAG,cAAc,GACvCkB,KAAK,CAAClB,IAAI,KAAK,SAAS,GAAG,iBAAiB,GAC5C,eAAe;gBACd;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3B,OAAA;gBAAKoB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrB,OAAA;kBAAIoB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEU,KAAK,CAACjB;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpE3B,OAAA;kBAAGoB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEU,KAAK,CAACb;gBAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7D3B,OAAA;kBAAMoB,SAAS,EAAE,gFACfW,KAAK,CAACZ,QAAQ,KAAK,MAAM,GAAG,yBAAyB,GACrDY,KAAK,CAACZ,QAAQ,KAAK,OAAO,GAAG,+BAA+B,GAC5D,2BAA2B,EAC1B;kBAAAE,QAAA,GAAC,uCACK,EAACU,KAAK,CAACZ,QAAQ;gBAAA;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAxBEI,KAAK,CAACnB,EAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAzMID,SAAS;EAAA,QACAT,WAAW;AAAA;AAAAwC,EAAA,GADpB/B,SAAS;AA2Mf,eAAeA,SAAS;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
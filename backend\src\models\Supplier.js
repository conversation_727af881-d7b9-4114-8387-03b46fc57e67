const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Supplier = sequelize.define('Supplier', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    supplierCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'supplier_code',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [2, 200],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM,
      values: ['individual', 'company'],
      defaultValue: 'company',
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    fax: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    website: {
      type: DataTypes.STRING(200),
      allowNull: true,
      validate: {
        isUrl: true,
        len: [5, 200]
      }
    },
    taxNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'tax_number',
      validate: {
        len: [5, 50]
      }
    },
    registrationNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'registration_number',
      validate: {
        len: [5, 50]
      }
    },
    // Address
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    state: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    country: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    postalCode: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'postal_code',
      validate: {
        len: [3, 20]
      }
    },
    // Contact Person
    contactPersonName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_name',
      validate: {
        len: [2, 100]
      }
    },
    contactPersonTitle: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_title',
      validate: {
        len: [2, 100]
      }
    },
    contactPersonEmail: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_email',
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    contactPersonPhone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'contact_person_phone',
      validate: {
        len: [10, 20]
      }
    },
    // Financial Information
    currentBalance: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'current_balance'
    },
    paymentTerms: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'payment_terms',
      validate: {
        len: [2, 100]
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    // Banking Information
    bankName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'bank_name',
      validate: {
        len: [2, 100]
      }
    },
    bankAccountNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'bank_account_number',
      validate: {
        len: [5, 50]
      }
    },
    bankRoutingNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'bank_routing_number',
      validate: {
        len: [5, 50]
      }
    },
    // Supplier Rating and Performance
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      }
    },
    qualityRating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'quality_rating',
      validate: {
        min: 1,
        max: 5
      }
    },
    deliveryRating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'delivery_rating',
      validate: {
        min: 1,
        max: 5
      }
    },
    serviceRating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'service_rating',
      validate: {
        min: 1,
        max: 5
      }
    },
    // Categories and Specialties
    categories: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    specialties: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    // Status and Notes
    status: {
      type: DataTypes.ENUM,
      values: ['active', 'inactive', 'blocked'],
      defaultValue: 'active',
      allowNull: false
    },
    isPreferred: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_preferred'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    internalNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'internal_notes'
    }
  }, {
    tableName: 'suppliers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['supplier_code']
      },
      {
        fields: ['name']
      },
      {
        fields: ['email']
      },
      {
        fields: ['status']
      },
      {
        fields: ['type']
      },
      {
        fields: ['is_preferred']
      }
    ]
  });

  // Instance methods
  Supplier.prototype.getFullAddress = function() {
    const parts = [this.address, this.city, this.state, this.country, this.postalCode].filter(Boolean);
    return parts.join(', ');
  };

  Supplier.prototype.getOverallRating = function() {
    const ratings = [this.qualityRating, this.deliveryRating, this.serviceRating].filter(Boolean);
    if (ratings.length === 0) return null;
    return (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(1);
  };

  // Associations
  Supplier.associate = function(models) {
    // Supplier has many Invoices (purchase invoices)
    if (models.Invoice) {
      Supplier.hasMany(models.Invoice, {
        foreignKey: 'supplier_id',
        as: 'invoices'
      });
    }

    // Supplier has many PurchaseOrders
    if (models.PurchaseOrder) {
      Supplier.hasMany(models.PurchaseOrder, {
        foreignKey: 'supplier_id',
        as: 'purchaseOrders'
      });
    }
  };

  return Supplier;
};

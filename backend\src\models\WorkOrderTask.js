const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkOrderTask = sequelize.define('WorkOrderTask', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    taskNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'task_number',
      validate: {
        min: 1
      }
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [3, 200],
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM,
      values: ['pending', 'in_progress', 'completed', 'skipped'],
      defaultValue: 'pending',
      allowNull: false
    },
    estimatedHours: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      field: 'estimated_hours',
      validate: {
        min: 0
      }
    },
    actualHours: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      field: 'actual_hours',
      validate: {
        min: 0
      }
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date'
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'end_date'
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    completionNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'completion_notes'
    },
    isRequired: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_required'
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'sort_order'
    }
  }, {
    tableName: 'work_order_tasks',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['work_order_id']
      },
      {
        fields: ['assigned_to']
      },
      {
        fields: ['status']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  // Instance methods
  WorkOrderTask.prototype.isCompleted = function() {
    return this.status === 'completed';
  };

  WorkOrderTask.prototype.isOverdue = function() {
    if (!this.endDate || this.status === 'completed' || this.status === 'skipped') {
      return false;
    }
    return new Date() > new Date(this.endDate);
  };

  WorkOrderTask.prototype.getDuration = function() {
    if (!this.startDate || !this.endDate) return null;
    const start = new Date(this.startDate);
    const end = new Date(this.endDate);
    return Math.ceil((end - start) / (1000 * 60 * 60)); // Duration in hours
  };

  // Associations
  WorkOrderTask.associate = function(models) {
    // WorkOrderTask belongs to WorkOrder
    if (models.WorkOrder) {
      WorkOrderTask.belongsTo(models.WorkOrder, {
        foreignKey: 'work_order_id',
        as: 'workOrder'
      });
    }

    // WorkOrderTask belongs to User (assigned to)
    if (models.User) {
      WorkOrderTask.belongsTo(models.User, {
        foreignKey: 'assigned_to',
        as: 'assignedUser'
      });
    }
  };

  return WorkOrderTask;
};

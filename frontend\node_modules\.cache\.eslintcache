[{"C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\utils\\i18n.js": "3", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Dashboard\\Dashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Equipment\\Equipment.js": "6", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Auth\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Reports\\Reports.js": "8", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Inventory\\Inventory.js": "9", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Maintenance\\Maintenance.js": "10", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Settings\\Settings.js": "11", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Accounting\\Accounting.js": "12", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Users\\Users.js": "13", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Layout.js": "14", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Auth\\ProtectedRoute.js": "15", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\uiSlice.js": "17", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Header.js": "18", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Sidebar.js": "19", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\UI\\NotificationContainer.js": "20", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\authService.js": "21", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\api.js": "22", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\equipmentSlice.js": "23", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\equipmentService.js": "24", "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\UI\\LoadingSpinner.js": "25"}, {"size": 307, "mtime": 1752931765340, "results": "26", "hashOfConfig": "27"}, {"size": 3097, "mtime": 1752931758661, "results": "28", "hashOfConfig": "27"}, {"size": 2305, "mtime": 1752936822330, "results": "29", "hashOfConfig": "27"}, {"size": 588, "mtime": 1752939720949, "results": "30", "hashOfConfig": "27"}, {"size": 7398, "mtime": 1752936977129, "results": "31", "hashOfConfig": "27"}, {"size": 17469, "mtime": 1752939853366, "results": "32", "hashOfConfig": "27"}, {"size": 6196, "mtime": 1752936493287, "results": "33", "hashOfConfig": "27"}, {"size": 1136, "mtime": 1752936745865, "results": "34", "hashOfConfig": "27"}, {"size": 1342, "mtime": 1752936697007, "results": "35", "hashOfConfig": "27"}, {"size": 1145, "mtime": 1752936711999, "results": "36", "hashOfConfig": "27"}, {"size": 1003, "mtime": 1752936803011, "results": "37", "hashOfConfig": "27"}, {"size": 1152, "mtime": 1752936730404, "results": "38", "hashOfConfig": "27"}, {"size": 1133, "mtime": 1752936787343, "results": "39", "hashOfConfig": "27"}, {"size": 931, "mtime": 1752936517176, "results": "40", "hashOfConfig": "27"}, {"size": 1644, "mtime": 1752936460423, "results": "41", "hashOfConfig": "27"}, {"size": 5598, "mtime": 1752936289044, "results": "42", "hashOfConfig": "27"}, {"size": 2387, "mtime": 1752936306506, "results": "43", "hashOfConfig": "27"}, {"size": 5513, "mtime": 1752938170121, "results": "44", "hashOfConfig": "27"}, {"size": 5759, "mtime": 1752936573366, "results": "45", "hashOfConfig": "27"}, {"size": 2992, "mtime": 1752936623091, "results": "46", "hashOfConfig": "27"}, {"size": 1221, "mtime": 1752936327868, "results": "47", "hashOfConfig": "27"}, {"size": 3104, "mtime": 1752938038983, "results": "48", "hashOfConfig": "27"}, {"size": 9756, "mtime": 1752939677840, "results": "49", "hashOfConfig": "27"}, {"size": 4539, "mtime": 1752939640971, "results": "50", "hashOfConfig": "27"}, {"size": 540, "mtime": 1752939840249, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "99fjr6", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\utils\\i18n.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Dashboard\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Equipment\\Equipment.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Reports\\Reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Inventory\\Inventory.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Maintenance\\Maintenance.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Settings\\Settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Accounting\\Accounting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\pages\\Users\\Users.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\uiSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\Layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\UI\\NotificationContainer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\store\\slices\\equipmentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\services\\equipmentService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Fady\\frontend\\src\\components\\UI\\LoadingSpinner.js", [], []]
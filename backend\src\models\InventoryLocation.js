const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InventoryLocation = sequelize.define('InventoryLocation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    code: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM('warehouse', 'shelf', 'bin', 'zone'),
      allowNull: false
    },
    parentId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'parent_id',
      references: {
        model: 'inventory_locations',
        key: 'id'
      }
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    capacity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'inventory_locations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['code']
      },
      {
        fields: ['parent_id']
      },
      {
        fields: ['type']
      }
    ]
  });

  // Instance methods
  InventoryLocation.prototype.getFullPath = async function() {
    let path = [this.name];
    let current = this;
    
    while (current.parentId) {
      current = await InventoryLocation.findByPk(current.parentId);
      if (current) {
        path.unshift(current.name);
      } else {
        break;
      }
    }
    
    return path.join(' > ');
  };

  InventoryLocation.prototype.getTotalStock = async function() {
    const { InventoryLocationStock } = require('./index');
    const stocks = await InventoryLocationStock.findAll({
      where: { location_id: this.id }
    });
    
    return stocks.reduce((total, stock) => total + parseFloat(stock.quantity), 0);
  };

  // Associations
  InventoryLocation.associate = function(models) {
    // Self-referencing association for parent-child relationship
    InventoryLocation.belongsTo(InventoryLocation, {
      foreignKey: 'parent_id',
      as: 'parent'
    });

    InventoryLocation.hasMany(InventoryLocation, {
      foreignKey: 'parent_id',
      as: 'children'
    });

    // InventoryLocation has many InventoryLocationStock
    if (models.InventoryLocationStock) {
      InventoryLocation.hasMany(models.InventoryLocationStock, {
        foreignKey: 'location_id',
        as: 'stocks'
      });
    }
  };

  return InventoryLocation;
};

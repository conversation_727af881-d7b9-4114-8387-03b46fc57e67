'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('inventory_transactions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      item_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'inventory_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM('in', 'out', 'adjustment', 'transfer'),
        allowNull: false
      },
      quantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      unit_cost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      total_cost: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0
      },
      balance_after: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      reference: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      reference_type: {
        type: Sequelize.ENUM('work_order', 'purchase_order', 'adjustment', 'transfer', 'manual'),
        allowNull: true
      },
      reference_id: {
        type: Sequelize.UUID,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      performed_by: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      location_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'inventory_locations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      batch_number: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      expiry_date: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // إضافة فهارس
    await queryInterface.addIndex('inventory_transactions', ['item_id']);
    await queryInterface.addIndex('inventory_transactions', ['type']);
    await queryInterface.addIndex('inventory_transactions', ['reference_type', 'reference_id']);
    await queryInterface.addIndex('inventory_transactions', ['performed_by']);
    await queryInterface.addIndex('inventory_transactions', ['location_id']);
    await queryInterface.addIndex('inventory_transactions', ['created_at']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('inventory_transactions');
  }
};

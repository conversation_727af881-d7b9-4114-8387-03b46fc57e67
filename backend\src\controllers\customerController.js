const { Customer, Equipment } = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع العملاء
const getAllCustomers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      type = '',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { customerCode: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } },
        { contactPersonName: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (type) {
      whereClause.type = type;
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy === 'created_at' ? 'created_at' : sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على عميل واحد
const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id, {
      include: [
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id', 'name', 'equipment_number', 'status', 'category']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.json({
      success: true,
      data: { customer }
    });

  } catch (error) {
    console.error('Get customer by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء عميل جديد
const createCustomer = async (req, res) => {
  try {
    const {
      customerCode,
      name,
      type = 'company',
      email,
      phone,
      mobile,
      taxNumber,
      billingAddress,
      billingCity,
      billingCountry,
      contactPersonName,
      contactPersonPhone,
      creditLimit = 0,
      notes
    } = req.body;

    // التحقق من عدم تكرار رمز العميل
    const existingCustomer = await Customer.findOne({
      where: { customerCode }
    });

    if (existingCustomer) {
      return res.status(400).json({
        success: false,
        message: 'Customer code already exists'
      });
    }

    // إنشاء العميل
    const customer = await Customer.create({
      customerCode,
      name,
      type,
      email,
      phone,
      mobile,
      taxNumber,
      billingAddress,
      billingCity,
      billingCountry,
      contactPersonName,
      contactPersonPhone,
      creditLimit,
      notes
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث عميل
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // التحقق من عدم تكرار رمز العميل
    if (updateData.customerCode && updateData.customerCode !== customer.customerCode) {
      const existingCustomer = await Customer.findOne({
        where: { 
          customerCode: updateData.customerCode,
          id: { [Op.ne]: id }
        }
      });

      if (existingCustomer) {
        return res.status(400).json({
          success: false,
          message: 'Customer code already exists'
        });
      }
    }

    // تحديث العميل
    await customer.update(updateData);

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer }
    });

  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف عميل
const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id, {
      include: [
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // التحقق من عدم وجود معدات مرتبطة بالعميل
    if (customer.equipment && customer.equipment.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer that has equipment assigned'
      });
    }

    await customer.destroy();

    res.json({
      success: true,
      message: 'Customer deleted successfully'
    });

  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على قائمة مبسطة من العملاء للاختيار
const getCustomersForSelect = async (req, res) => {
  try {
    const { search = '' } = req.query;
    
    const whereClause = {
      status: 'active'
    };
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { customerCode: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const customers = await Customer.findAll({
      where: whereClause,
      attributes: ['id', 'name', 'customer_code'],
      order: [['name', 'ASC']],
      limit: 50
    });

    res.json({
      success: true,
      data: { customers }
    });

  } catch (error) {
    console.error('Get customers for select error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomersForSelect
};

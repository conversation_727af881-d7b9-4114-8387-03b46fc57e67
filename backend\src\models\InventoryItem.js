const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InventoryItem = sequelize.define('InventoryItem', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    itemCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'item_code',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [2, 50],
        notEmpty: true,
        isIn: [['spare_parts', 'consumables', 'tools', 'raw_materials', 'finished_goods', 'other']]
      }
    },
    subcategory: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [2, 50]
      }
    },
    unit: {
      type: DataTypes.STRING(20),
      allowNull: false,
      validate: {
        len: [1, 20],
        notEmpty: true,
        isIn: [['piece', 'kg', 'liter', 'meter', 'box', 'set', 'roll', 'bottle', 'gallon']]
      }
    },
    currentStock: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'current_stock',
      validate: {
        min: 0
      }
    },
    minimumStock: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'minimum_stock',
      validate: {
        min: 0
      }
    },
    maximumStock: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'maximum_stock',
      validate: {
        min: 0
      }
    },
    reorderLevel: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'reorder_level',
      validate: {
        min: 0
      }
    },
    reorderQuantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'reorder_quantity',
      validate: {
        min: 0
      }
    },
    unitCost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'unit_cost',
      validate: {
        min: 0
      }
    },
    averageCost: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
      field: 'average_cost',
      validate: {
        min: 0
      }
    },
    lastPurchasePrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'last_purchase_price',
      validate: {
        min: 0
      }
    },
    sellingPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'selling_price',
      validate: {
        min: 0
      }
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    shelf: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [1, 50]
      }
    },
    barcode: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true,
      validate: {
        len: [5, 100]
      }
    },
    supplier: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    leadTime: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'lead_time',
      comment: 'Lead time in days',
      validate: {
        min: 0
      }
    },
    expiryDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'expiry_date'
    },
    batchNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'batch_number',
      validate: {
        len: [2, 50]
      }
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    specifications: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {}
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    isTracked: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_tracked',
      comment: 'Whether to track stock levels for this item'
    }
  }, {
    tableName: 'inventory_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['item_code']
      },
      {
        fields: ['category']
      },
      {
        fields: ['barcode']
      },
      {
        fields: ['current_stock']
      },
      {
        fields: ['minimum_stock']
      }
    ]
  });

  // Instance methods
  InventoryItem.prototype.isLowStock = function() {
    return this.currentStock <= this.minimumStock;
  };

  InventoryItem.prototype.isOutOfStock = function() {
    return this.currentStock <= 0;
  };

  InventoryItem.prototype.needsReorder = function() {
    return this.reorderLevel && this.currentStock <= this.reorderLevel;
  };

  InventoryItem.prototype.getTotalValue = function() {
    return this.currentStock * this.averageCost;
  };

  // Associations
  InventoryItem.associate = function(models) {
    // InventoryItem has many InventoryTransactions
    InventoryItem.hasMany(models.InventoryTransaction, {
      foreignKey: 'item_id',
      as: 'transactions'
    });

    // InventoryItem has many WorkOrderParts
    InventoryItem.hasMany(models.WorkOrderPart, {
      foreignKey: 'item_id',
      as: 'workOrderParts'
    });

    // InventoryItem has many InvoiceItems
    InventoryItem.hasMany(models.InvoiceItem, {
      foreignKey: 'item_id',
      as: 'invoiceItems'
    });
  };

  return InventoryItem;
};

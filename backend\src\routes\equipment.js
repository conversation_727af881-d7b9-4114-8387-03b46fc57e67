const express = require('express');
const router = express.Router();

// Controllers
const equipmentController = require('../controllers/equipmentController');

// Middleware
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');

/**
 * @route   GET /api/equipment/stats
 * @desc    Get equipment statistics
 * @access  Private (requires equipment.read permission)
 */
router.get('/stats', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  equipmentController.getEquipmentStats
);

/**
 * @route   GET /api/equipment
 * @desc    Get all equipment
 * @access  Private (requires equipment.read permission)
 */
router.get('/', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  equipmentController.getAllEquipment
);

/**
 * @route   GET /api/equipment/:id
 * @desc    Get equipment by ID
 * @access  Private (requires equipment.read permission)
 */
router.get('/:id', 
  authenticateToken, 
  requirePermission('equipment.read'), 
  equipmentController.getEquipmentById
);

/**
 * @route   POST /api/equipment
 * @desc    Create new equipment
 * @access  Private (requires equipment.create permission)
 */
router.post('/', 
  authenticateToken, 
  requirePermission('equipment.create'), 
  equipmentController.createEquipment
);

/**
 * @route   PUT /api/equipment/:id
 * @desc    Update equipment
 * @access  Private (requires equipment.update permission)
 */
router.put('/:id', 
  authenticateToken, 
  requirePermission('equipment.update'), 
  equipmentController.updateEquipment
);

/**
 * @route   DELETE /api/equipment/:id
 * @desc    Delete equipment
 * @access  Private (requires equipment.delete permission)
 */
router.delete('/:id', 
  authenticateToken, 
  requirePermission('equipment.delete'), 
  equipmentController.deleteEquipment
);

module.exports = router;

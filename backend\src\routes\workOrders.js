const express = require('express');
const router = express.Router();

// Controllers
const workOrderController = require('../controllers/workOrderController');

// Middleware
const { 
  authenticateToken, 
  requirePermission 
} = require('../middleware/auth');

/**
 * @route   GET /api/work-orders/stats
 * @desc    Get work orders statistics
 * @access  Private (requires maintenance.read permission)
 */
router.get('/stats', 
  authenticateToken, 
  requirePermission('maintenance.read'), 
  workOrderController.getWorkOrderStats
);

/**
 * @route   GET /api/work-orders
 * @desc    Get all work orders
 * @access  Private (requires maintenance.read permission)
 */
router.get('/', 
  authenticateToken, 
  requirePermission('maintenance.read'), 
  workOrderController.getAllWorkOrders
);

/**
 * @route   GET /api/work-orders/:id
 * @desc    Get work order by ID
 * @access  Private (requires maintenance.read permission)
 */
router.get('/:id', 
  authenticateToken, 
  requirePermission('maintenance.read'), 
  workOrderController.getWorkOrderById
);

/**
 * @route   POST /api/work-orders
 * @desc    Create new work order
 * @access  Private (requires maintenance.create permission)
 */
router.post('/', 
  authenticateToken, 
  requirePermission('maintenance.create'), 
  workOrderController.createWorkOrder
);

/**
 * @route   PUT /api/work-orders/:id
 * @desc    Update work order
 * @access  Private (requires maintenance.update permission)
 */
router.put('/:id', 
  authenticateToken, 
  requirePermission('maintenance.update'), 
  workOrderController.updateWorkOrder
);

/**
 * @route   DELETE /api/work-orders/:id
 * @desc    Delete work order
 * @access  Private (requires maintenance.delete permission)
 */
router.delete('/:id', 
  authenticateToken, 
  requirePermission('maintenance.delete'), 
  workOrderController.deleteWorkOrder
);

module.exports = router;

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // إنشاء جدول فئات المخزون
    await queryInterface.createTable('inventory_categories', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      name_ar: {
        type: Sequelize.STRING(100),
        allowNull: true
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      parent_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'inventory_categories',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // إنشاء جدول وحدات القياس
    await queryInterface.createTable('units_of_measure', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      name_ar: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      abbreviation: {
        type: Sequelize.STRING(10),
        allowNull: false,
        unique: true
      },
      type: {
        type: Sequelize.ENUM('weight', 'length', 'volume', 'area', 'count', 'time'),
        allowNull: false
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // تحديث جدول inventory_items
    await queryInterface.addColumn('inventory_items', 'category_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'inventory_categories',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('inventory_items', 'unit_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'units_of_measure',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // التحقق من وجود العمود قبل الإضافة
    const tableDescription = await queryInterface.describeTable('inventory_items');

    if (!tableDescription.barcode) {
      await queryInterface.addColumn('inventory_items', 'barcode', {
        type: Sequelize.STRING(100),
        allowNull: true,
        unique: true
      });
    }

    if (!tableDescription.shelf_life_days) {
      await queryInterface.addColumn('inventory_items', 'shelf_life_days', {
        type: Sequelize.INTEGER,
        allowNull: true
      });
    }

    if (!tableDescription.weight) {
      await queryInterface.addColumn('inventory_items', 'weight', {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true
      });
    }

    if (!tableDescription.dimensions) {
      await queryInterface.addColumn('inventory_items', 'dimensions', {
        type: Sequelize.JSON,
        allowNull: true
      });
    }

    if (!tableDescription.images) {
      await queryInterface.addColumn('inventory_items', 'images', {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      });
    }

    if (!tableDescription.specifications) {
      await queryInterface.addColumn('inventory_items', 'specifications', {
        type: Sequelize.JSON,
        allowNull: true
      });
    }

    if (!tableDescription.compatible_equipment) {
      await queryInterface.addColumn('inventory_items', 'compatible_equipment', {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      });
    }

    // إنشاء جدول مواقع المخزون
    await queryInterface.createTable('inventory_locations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      code: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true
      },
      type: {
        type: Sequelize.ENUM('warehouse', 'shelf', 'bin', 'zone'),
        allowNull: false
      },
      parent_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'inventory_locations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      capacity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // إنشاء جدول مخزون المواقع
    await queryInterface.createTable('inventory_location_stock', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      item_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'inventory_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      location_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'inventory_locations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      reserved_quantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // إضافة فهارس
    await queryInterface.addIndex('inventory_categories', ['parent_id']);
    await queryInterface.addIndex('inventory_items', ['category_id']);
    await queryInterface.addIndex('inventory_items', ['unit_id']);
    await queryInterface.addIndex('inventory_items', ['barcode']);
    await queryInterface.addIndex('inventory_locations', ['parent_id']);
    await queryInterface.addIndex('inventory_locations', ['code']);
    await queryInterface.addIndex('inventory_location_stock', ['item_id']);
    await queryInterface.addIndex('inventory_location_stock', ['location_id']);
    await queryInterface.addIndex('inventory_location_stock', ['item_id', 'location_id'], { unique: true });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('inventory_location_stock');
    await queryInterface.dropTable('inventory_locations');
    
    // إزالة الأعمدة المضافة
    await queryInterface.removeColumn('inventory_items', 'compatible_equipment');
    await queryInterface.removeColumn('inventory_items', 'specifications');
    await queryInterface.removeColumn('inventory_items', 'images');
    await queryInterface.removeColumn('inventory_items', 'dimensions');
    await queryInterface.removeColumn('inventory_items', 'weight');
    await queryInterface.removeColumn('inventory_items', 'shelf_life_days');
    await queryInterface.removeColumn('inventory_items', 'location');
    await queryInterface.removeColumn('inventory_items', 'barcode');
    await queryInterface.removeColumn('inventory_items', 'unit_id');
    await queryInterface.removeColumn('inventory_items', 'category_id');
    
    await queryInterface.dropTable('units_of_measure');
    await queryInterface.dropTable('inventory_categories');
  }
};

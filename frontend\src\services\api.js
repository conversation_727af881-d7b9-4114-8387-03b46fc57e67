import axios from 'axios';
import { store } from '../store/store';
import { logout } from '../store/slices/authSlice';
import { addNotification } from '../store/slices/uiSlice';

// إنشاء instance من axios
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor لإضافة التوكن
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response) {
      const { status, data } = response;
      
      // التعامل مع أخطاء المصادقة
      if (status === 401) {
        // إذا كان التوكن منتهي الصلاحية أو غير صحيح
        if (data.message === 'Token expired' || data.message === 'Invalid token') {
          store.dispatch(logout());
          store.dispatch(addNotification({
            type: 'error',
            title: 'انتهت جلسة العمل',
            message: 'يرجى تسجيل الدخول مرة أخرى',
            duration: 5000,
          }));
          // إعادة توجيه لصفحة تسجيل الدخول
          window.location.href = '/login';
        }
      }
      
      // التعامل مع أخطاء الصلاحيات
      else if (status === 403) {
        store.dispatch(addNotification({
          type: 'error',
          title: 'غير مصرح',
          message: data.message || 'ليس لديك صلاحية للوصول لهذا المورد',
          duration: 5000,
        }));
      }
      
      // التعامل مع أخطاء الخادم
      else if (status >= 500) {
        store.dispatch(addNotification({
          type: 'error',
          title: 'خطأ في الخادم',
          message: 'حدث خطأ في الخادم، يرجى المحاولة لاحقاً',
          duration: 5000,
        }));
      }
      
      // التعامل مع أخطاء التحقق من صحة البيانات
      else if (status === 400 && data.errors) {
        const errorMessages = data.errors.map(err => err.message).join(', ');
        store.dispatch(addNotification({
          type: 'error',
          title: 'خطأ في البيانات',
          message: errorMessages,
          duration: 7000,
        }));
      }
    } else if (error.request) {
      // خطأ في الشبكة
      store.dispatch(addNotification({
        type: 'error',
        title: 'خطأ في الاتصال',
        message: 'تعذر الاتصال بالخادم، تحقق من اتصال الإنترنت',
        duration: 5000,
      }));
    }
    
    return Promise.reject(error);
  }
);

export default api;

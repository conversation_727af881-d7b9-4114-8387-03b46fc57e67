{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\nimport uiSlice from './slices/uiSlice';\nimport equipmentReducer from './slices/equipmentSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n    ui: uiSlice\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST']\n    }\n  })\n});", "map": {"version": 3, "names": ["configureStore", "authSlice", "uiSlice", "equipmentReducer", "store", "reducer", "auth", "ui", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/store/store.js"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport authSlice from './slices/authSlice';\nimport uiSlice from './slices/uiSlice';\nimport equipmentReducer from './slices/equipmentSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n    ui: uiSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST'],\n      },\n    }),\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,gBAAgB,MAAM,yBAAyB;AAEtD,OAAO,MAAMC,KAAK,GAAGJ,cAAc,CAAC;EAClCK,OAAO,EAAE;IACPC,IAAI,EAAEL,SAAS;IACfM,EAAE,EAAEL;EACN,CAAC;EACDM,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB;IACpC;EACF,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
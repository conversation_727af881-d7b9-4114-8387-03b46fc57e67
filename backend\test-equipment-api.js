const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api';

async function testEquipmentAPIs() {
  console.log('🧪 Testing Equipment APIs...\n');

  try {
    // 1. تسجيل الدخول للحصول على التوكن
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful!\n');

    const headers = {
      'Authorization': `Bearer ${token}`
    };

    // 2. اختبار إحصائيات المعدات
    console.log('2️⃣ Testing Equipment Stats...');
    const statsResponse = await axios.get(`${BASE_URL}/equipment/stats`, { headers });
    console.log('✅ Equipment Stats:');
    console.log('Total Equipment:', statsResponse.data.data.totalEquipment);
    console.log('Maintenance Due:', statsResponse.data.data.maintenanceDue);
    console.log('Status Stats:', statsResponse.data.data.statusStats);
    console.log('Category Stats:', statsResponse.data.data.categoryStats);
    console.log('');

    // 3. اختبار الحصول على جميع المعدات
    console.log('3️⃣ Testing Get All Equipment...');
    const equipmentResponse = await axios.get(`${BASE_URL}/equipment?limit=5`, { headers });
    console.log('✅ Equipment retrieved!');
    console.log('Total items:', equipmentResponse.data.data.pagination.totalItems);
    console.log('Equipment list:');
    equipmentResponse.data.data.equipment.forEach(eq => {
      console.log(`  - ${eq.name} (${eq.equipment_number}) - ${eq.status} - Customer: ${eq.customer?.name || 'None'}`);
    });
    console.log('');

    // 4. اختبار البحث في المعدات
    console.log('4️⃣ Testing Equipment Search...');
    const searchResponse = await axios.get(`${BASE_URL}/equipment?search=كاتربيلر`, { headers });
    console.log('✅ Search results:');
    searchResponse.data.data.equipment.forEach(eq => {
      console.log(`  - ${eq.name} (${eq.brand})`);
    });
    console.log('');

    // 5. اختبار فلترة المعدات حسب الفئة
    console.log('5️⃣ Testing Equipment Filter by Category...');
    const filterResponse = await axios.get(`${BASE_URL}/equipment?category=excavator`, { headers });
    console.log('✅ Excavators found:', filterResponse.data.data.pagination.totalItems);
    filterResponse.data.data.equipment.forEach(eq => {
      console.log(`  - ${eq.name} (${eq.category})`);
    });
    console.log('');

    // 6. اختبار الحصول على العملاء
    console.log('6️⃣ Testing Get All Customers...');
    const customersResponse = await axios.get(`${BASE_URL}/customers`, { headers });
    console.log('✅ Customers retrieved!');
    console.log('Total customers:', customersResponse.data.data.pagination.totalItems);
    customersResponse.data.data.customers.forEach(customer => {
      console.log(`  - ${customer.name} (${customer.customer_code}) - ${customer.type}`);
    });
    console.log('');

    // 7. اختبار الحصول على عميل واحد مع معداته
    console.log('7️⃣ Testing Get Customer with Equipment...');
    const firstCustomerId = customersResponse.data.data.customers[0].id;
    const customerResponse = await axios.get(`${BASE_URL}/customers/${firstCustomerId}`, { headers });
    const customer = customerResponse.data.data.customer;
    console.log('✅ Customer details:');
    console.log(`Name: ${customer.name}`);
    console.log(`Code: ${customer.customer_code}`);
    console.log(`Equipment count: ${customer.equipment?.length || 0}`);
    if (customer.equipment && customer.equipment.length > 0) {
      console.log('Equipment:');
      customer.equipment.forEach(eq => {
        console.log(`  - ${eq.name} (${eq.equipment_number})`);
      });
    }
    console.log('');

    // 8. اختبار إنشاء معدة جديدة
    console.log('8️⃣ Testing Create New Equipment...');
    const newEquipment = {
      equipmentNumber: 'TEST001',
      name: 'معدة اختبار',
      description: 'معدة للاختبار فقط',
      category: 'other',
      brand: 'Test Brand',
      model: 'Test Model',
      status: 'operational',
      condition: 'good',
      location: 'مستودع الاختبار',
      notes: 'معدة تجريبية'
    };

    const createResponse = await axios.post(`${BASE_URL}/equipment`, newEquipment, { headers });
    console.log('✅ Equipment created successfully!');
    console.log('New equipment ID:', createResponse.data.data.equipment.id);
    console.log('Equipment number:', createResponse.data.data.equipment.equipment_number);
    console.log('');

    // 9. اختبار تحديث المعدة
    console.log('9️⃣ Testing Update Equipment...');
    const equipmentId = createResponse.data.data.equipment.id;
    const updateData = {
      name: 'معدة اختبار محدثة',
      operatingHours: 100,
      notes: 'تم تحديث المعدة بنجاح'
    };

    const updateResponse = await axios.put(`${BASE_URL}/equipment/${equipmentId}`, updateData, { headers });
    console.log('✅ Equipment updated successfully!');
    console.log('Updated name:', updateResponse.data.data.equipment.name);
    console.log('Operating hours:', updateResponse.data.data.equipment.operating_hours);
    console.log('');

    // 10. اختبار حذف المعدة
    console.log('🔟 Testing Delete Equipment...');
    await axios.delete(`${BASE_URL}/equipment/${equipmentId}`, { headers });
    console.log('✅ Equipment deleted successfully!');
    console.log('');

    console.log('🎉 All Equipment API tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.errors) {
      console.error('Validation errors:', error.response.data.errors);
    }
  }
}

// تشغيل الاختبارات
testEquipmentAPIs();

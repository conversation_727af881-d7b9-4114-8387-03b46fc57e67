{"name": "heavy-equipment-erp-frontend", "version": "1.0.0", "description": "Frontend for Heavy Equipment ERP System", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "axios": "^1.6.2", "tailwindcss": "^3.3.6", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-i18next": "^13.5.0", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "react-table": "^7.8.0", "react-datepicker": "^4.25.0", "file-saver": "^2.0.5", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "proxy": "http://localhost:5001"}
import api from './api';

// Equipment API calls
export const equipmentService = {
  // Get all equipment with filters and pagination
  getAll: async (params = {}) => {
    const response = await api.get('/equipment', { params });
    return response.data;
  },

  // Get equipment by ID
  getById: async (id) => {
    const response = await api.get(`/equipment/${id}`);
    return response.data;
  },

  // Create new equipment
  create: async (equipmentData) => {
    const response = await api.post('/equipment', equipmentData);
    return response.data;
  },

  // Update equipment
  update: async (id, equipmentData) => {
    const response = await api.put(`/equipment/${id}`, equipmentData);
    return response.data;
  },

  // Delete equipment
  delete: async (id) => {
    const response = await api.delete(`/equipment/${id}`);
    return response.data;
  },

  // Get equipment statistics
  getStats: async () => {
    const response = await api.get('/equipment/stats');
    return response.data;
  }
};

// Customer API calls
export const customerService = {
  // Get all customers with filters and pagination
  getAll: async (params = {}) => {
    const response = await api.get('/customers', { params });
    return response.data;
  },

  // Get customers for select dropdown
  getForSelect: async (search = '') => {
    const response = await api.get('/customers/select', { 
      params: { search } 
    });
    return response.data;
  },

  // Get customer by ID
  getById: async (id) => {
    const response = await api.get(`/customers/${id}`);
    return response.data;
  },

  // Create new customer
  create: async (customerData) => {
    const response = await api.post('/customers', customerData);
    return response.data;
  },

  // Update customer
  update: async (id, customerData) => {
    const response = await api.put(`/customers/${id}`, customerData);
    return response.data;
  },

  // Delete customer
  delete: async (id) => {
    const response = await api.delete(`/customers/${id}`);
    return response.data;
  }
};

// Equipment categories and statuses
export const equipmentCategories = [
  { value: 'excavator', label: 'حفارة', icon: '🚜' },
  { value: 'bulldozer', label: 'بلدوزر', icon: '🚛' },
  { value: 'crane', label: 'رافعة', icon: '🏗️' },
  { value: 'loader', label: 'لودر', icon: '🚚' },
  { value: 'truck', label: 'شاحنة', icon: '🚚' },
  { value: 'generator', label: 'مولد كهربائي', icon: '⚡' },
  { value: 'compressor', label: 'ضاغط هواء', icon: '💨' },
  { value: 'other', label: 'أخرى', icon: '🔧' }
];

export const equipmentStatuses = [
  { value: 'operational', label: 'تشغيلية', color: 'green' },
  { value: 'maintenance', label: 'صيانة', color: 'yellow' },
  { value: 'repair', label: 'إصلاح', color: 'red' },
  { value: 'out_of_service', label: 'خارج الخدمة', color: 'gray' },
  { value: 'sold', label: 'مباعة', color: 'blue' }
];

export const equipmentConditions = [
  { value: 'excellent', label: 'ممتازة', color: 'green' },
  { value: 'good', label: 'جيدة', color: 'blue' },
  { value: 'fair', label: 'مقبولة', color: 'yellow' },
  { value: 'poor', label: 'سيئة', color: 'red' }
];

// Helper functions
export const getEquipmentCategoryLabel = (category) => {
  const cat = equipmentCategories.find(c => c.value === category);
  return cat ? cat.label : category;
};

export const getEquipmentStatusLabel = (status) => {
  const stat = equipmentStatuses.find(s => s.value === status);
  return stat ? stat.label : status;
};

export const getEquipmentConditionLabel = (condition) => {
  const cond = equipmentConditions.find(c => c.value === condition);
  return cond ? cond.label : condition;
};

export const getStatusColor = (status) => {
  const stat = equipmentStatuses.find(s => s.value === status);
  return stat ? stat.color : 'gray';
};

export const getConditionColor = (condition) => {
  const cond = equipmentConditions.find(c => c.value === condition);
  return cond ? cond.color : 'gray';
};

// Format currency
export const formatCurrency = (amount, currency = 'SAR') => {
  if (!amount) return '0';
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

// Format operating hours
export const formatOperatingHours = (hours) => {
  if (!hours) return '0 ساعة';
  return `${hours.toLocaleString('ar-SA')} ساعة`;
};

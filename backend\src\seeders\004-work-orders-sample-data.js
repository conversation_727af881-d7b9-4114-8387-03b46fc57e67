const { v4: uuidv4 } = require('uuid');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      const now = new Date();

      // الحصول على معرفات العملاء والمعدات الموجودة
      const customers = await queryInterface.sequelize.query(
        'SELECT id FROM customers LIMIT 3',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const equipment = await queryInterface.sequelize.query(
        'SELECT id FROM equipment LIMIT 6',
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (customers.length === 0 || equipment.length === 0) {
        console.log('⚠️ No customers or equipment found. Skipping work orders seeding.');
        await transaction.commit();
        return;
      }

      // إنشاء أوامر صيانة تجريبية
      const workOrders = [
        {
          id: uuidv4(),
          work_order_number: 'WO-2024-001',
          customer_id: customers[0].id,
          equipment_id: equipment[0].id,
          received_date: '2024-01-15',
          received_by: 'أحمد محمد - فني الاستقبال',
          problem_description: 'المعدة لا تعمل بشكل طبيعي، صوت غريب من المحرك وضعف في القوة',
          customer_complaint: 'المعدة تصدر أصوات غريبة وقوتها ضعيفة، لا تستطيع رفع الأحمال الثقيلة',
          initial_diagnosis: 'مشكلة في النظام الهيدروليكي، تسريب في الخراطيم الرئيسية',
          estimated_cost: 15000.00,
          estimated_completion_date: '2024-01-25',
          status: 'in_progress',
          priority: 'high',
          labor_cost: 8000.00,
          parts_cost: 6500.00,
          other_costs: 500.00,
          total_cost: 15000.00,
          started_date: '2024-01-16',
          assigned_technician: 'محمد علي - فني هيدروليك',
          work_performed: 'تم فحص النظام الهيدروليكي وتبديل الخراطيم التالفة وتغيير الزيت',
          internal_notes: 'المعدة في حالة جيدة عموماً، المشكلة كانت في الخراطيم فقط',
          customer_notes: 'العميل راضي عن السرعة في التشخيص',
          warranty_period: 90,
          warranty_description: 'ضمان 3 أشهر على قطع الغيار والعمالة',
          before_images: JSON.stringify(['before1.jpg', 'before2.jpg']),
          after_images: JSON.stringify([]),
          documents: JSON.stringify(['invoice.pdf']),
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_number: 'WO-2024-002',
          customer_id: customers[1].id,
          equipment_id: equipment[1].id,
          received_date: '2024-01-18',
          received_by: 'سارة أحمد - فني الاستقبال',
          problem_description: 'المعدة تحتاج صيانة دورية شاملة وفحص عام',
          customer_complaint: 'صيانة دورية مجدولة حسب ساعات التشغيل',
          initial_diagnosis: 'صيانة دورية شاملة - تغيير الزيوت والفلاتر والفحص العام',
          estimated_cost: 8500.00,
          estimated_completion_date: '2024-01-22',
          status: 'completed',
          priority: 'medium',
          labor_cost: 3500.00,
          parts_cost: 4500.00,
          other_costs: 500.00,
          total_cost: 8500.00,
          started_date: '2024-01-19',
          completed_date: '2024-01-21',
          assigned_technician: 'خالد حسن - فني عام',
          work_performed: 'تم تغيير زيت المحرك والهيدروليك، تبديل جميع الفلاتر، فحص الفرامل والإطارات',
          internal_notes: 'المعدة في حالة ممتازة، تم إجراء الصيانة في الوقت المحدد',
          customer_notes: 'العميل منتظم في الصيانة الدورية',
          warranty_period: 30,
          warranty_description: 'ضمان شهر على قطع الغيار',
          before_images: JSON.stringify(['maintenance_before.jpg']),
          after_images: JSON.stringify(['maintenance_after.jpg']),
          documents: JSON.stringify(['maintenance_report.pdf']),
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_number: 'WO-2024-003',
          customer_id: customers[2].id,
          equipment_id: equipment[2].id,
          received_date: '2024-01-20',
          received_by: 'أحمد محمد - فني الاستقبال',
          problem_description: 'عطل في نظام التبريد، المحرك يسخن بسرعة',
          customer_complaint: 'المحرك يسخن بشكل مفرط ويتوقف عن العمل',
          initial_diagnosis: 'مشكلة في نظام التبريد - تسريب في الرادياتير',
          estimated_cost: 12000.00,
          estimated_completion_date: '2024-01-28',
          status: 'waiting_parts',
          priority: 'urgent',
          labor_cost: 4000.00,
          parts_cost: 7500.00,
          other_costs: 500.00,
          total_cost: 12000.00,
          started_date: '2024-01-21',
          assigned_technician: 'عمر فاروق - فني محركات',
          work_performed: 'تم فحص نظام التبريد وتحديد الأجزاء التالفة، في انتظار وصول الرادياتير الجديد',
          internal_notes: 'قطعة الرادياتير غير متوفرة محلياً، تم طلبها من المورد الرئيسي',
          customer_notes: 'العميل متفهم لتأخير قطع الغيار',
          warranty_period: 180,
          warranty_description: 'ضمان 6 أشهر على الرادياتير الجديد',
          before_images: JSON.stringify(['radiator_damage.jpg']),
          after_images: JSON.stringify([]),
          documents: JSON.stringify(['parts_order.pdf']),
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_number: 'WO-2024-004',
          customer_id: customers[0].id,
          equipment_id: equipment[3].id,
          received_date: '2024-01-22',
          received_by: 'سارة أحمد - فني الاستقبال',
          problem_description: 'فحص عام قبل البيع',
          customer_complaint: 'العميل يريد فحص شامل للمعدة قبل بيعها',
          initial_diagnosis: 'فحص شامل لجميع الأنظمة وتقييم الحالة العامة',
          estimated_cost: 2500.00,
          estimated_completion_date: '2024-01-24',
          status: 'diagnosed',
          priority: 'low',
          labor_cost: 2000.00,
          parts_cost: 0.00,
          other_costs: 500.00,
          total_cost: 2500.00,
          assigned_technician: 'محمد علي - فني عام',
          work_performed: 'تم الفحص الشامل وإعداد تقرير مفصل عن حالة المعدة',
          internal_notes: 'المعدة في حالة جيدة جداً، قيمتها السوقية عالية',
          customer_notes: 'العميل راضي عن التقرير المفصل',
          warranty_period: 0,
          warranty_description: 'فحص فقط - لا يوجد ضمان',
          before_images: JSON.stringify(['inspection1.jpg', 'inspection2.jpg']),
          after_images: JSON.stringify(['inspection_complete.jpg']),
          documents: JSON.stringify(['inspection_report.pdf']),
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_number: 'WO-2024-005',
          customer_id: customers[1].id,
          equipment_id: equipment[4].id,
          received_date: '2024-01-25',
          received_by: 'أحمد محمد - فني الاستقبال',
          problem_description: 'إصلاح أضرار حادث - تلف في الهيكل الخارجي',
          customer_complaint: 'المعدة تعرضت لحادث وتحتاج إصلاح الهيكل والفحص الشامل',
          initial_diagnosis: 'أضرار في الهيكل الخارجي، تحتاج لحام وإصلاح، فحص الأنظمة الداخلية',
          estimated_cost: 25000.00,
          estimated_completion_date: '2024-02-05',
          status: 'received',
          priority: 'medium',
          labor_cost: 15000.00,
          parts_cost: 8000.00,
          other_costs: 2000.00,
          total_cost: 25000.00,
          assigned_technician: 'يوسف أحمد - فني لحام',
          work_performed: '',
          internal_notes: 'حادث كبير، يحتاج وقت أطول للإصلاح',
          customer_notes: 'العميل لديه تأمين، سيتم التنسيق مع شركة التأمين',
          warranty_period: 365,
          warranty_description: 'ضمان سنة كاملة على أعمال اللحام والإصلاح',
          before_images: JSON.stringify(['accident1.jpg', 'accident2.jpg', 'accident3.jpg']),
          after_images: JSON.stringify([]),
          documents: JSON.stringify(['insurance_claim.pdf']),
          created_at: now,
          updated_at: now
        }
      ];

      await queryInterface.bulkInsert('work_orders', workOrders, { transaction });

      // إنشاء قطع غيار مستخدمة
      const workOrderParts = [
        {
          id: uuidv4(),
          work_order_id: workOrders[0].id,
          part_name: 'خرطوم هيدروليك رئيسي',
          part_number: 'HYD-001-CAT',
          quantity: 2,
          unit_price: 1500.00,
          total_price: 3000.00,
          supplier: 'شركة قطع غيار المعدات',
          notes: 'خرطوم أصلي من كاتربيلر',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_id: workOrders[0].id,
          part_name: 'زيت هيدروليك',
          part_number: 'OIL-HYD-20L',
          quantity: 3,
          unit_price: 800.00,
          total_price: 2400.00,
          supplier: 'شركة الزيوت المتخصصة',
          notes: 'زيت هيدروليك عالي الجودة',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_id: workOrders[1].id,
          part_name: 'فلتر زيت المحرك',
          part_number: 'ENG-FILTER-001',
          quantity: 1,
          unit_price: 450.00,
          total_price: 450.00,
          supplier: 'شركة الفلاتر المتقدمة',
          notes: 'فلتر أصلي',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_id: workOrders[1].id,
          part_name: 'زيت محرك',
          part_number: 'ENG-OIL-15W40',
          quantity: 4,
          unit_price: 200.00,
          total_price: 800.00,
          supplier: 'شركة الزيوت المتخصصة',
          notes: 'زيت محرك ديزل',
          created_at: now,
          updated_at: now
        }
      ];

      await queryInterface.bulkInsert('work_order_parts', workOrderParts, { transaction });

      // إنشاء سجلات العمل
      const workLogs = [
        {
          id: uuidv4(),
          work_order_id: workOrders[0].id,
          log_date: '2024-01-16T08:00:00Z',
          technician_name: 'محمد علي',
          hours_worked: 4.5,
          work_description: 'فحص النظام الهيدروليك وتحديد مصدر التسريب',
          status_before: 'received',
          status_after: 'diagnosed',
          notes: 'تم العثور على تسريب في الخرطوم الرئيسي',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_id: workOrders[0].id,
          log_date: '2024-01-17T08:00:00Z',
          technician_name: 'محمد علي',
          hours_worked: 6.0,
          work_description: 'تبديل الخراطيم التالفة وتغيير الزيت الهيدروليك',
          status_before: 'diagnosed',
          status_after: 'in_progress',
          notes: 'تم تركيب خراطيم جديدة وملء النظام بالزيت',
          created_at: now,
          updated_at: now
        },
        {
          id: uuidv4(),
          work_order_id: workOrders[1].id,
          log_date: '2024-01-19T08:00:00Z',
          technician_name: 'خالد حسن',
          hours_worked: 8.0,
          work_description: 'صيانة دورية شاملة - تغيير الزيوت والفلاتر',
          status_before: 'received',
          status_after: 'completed',
          notes: 'تم إنجاز الصيانة الدورية بنجاح',
          created_at: now,
          updated_at: now
        }
      ];

      await queryInterface.bulkInsert('work_logs', workLogs, { transaction });

      await transaction.commit();
      console.log('✅ Work Orders sample data seeded successfully');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error seeding work orders sample data:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      await queryInterface.bulkDelete('work_logs', null, { transaction });
      await queryInterface.bulkDelete('work_order_parts', null, { transaction });
      await queryInterface.bulkDelete('work_orders', null, { transaction });
      
      await transaction.commit();
      console.log('✅ Work Orders sample data removed successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing work orders sample data:', error);
      throw error;
    }
  }
};

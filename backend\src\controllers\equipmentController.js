const { Equipment, Customer } = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع المعدات
const getAllEquipment = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      category = '',
      status = '',
      customer = '',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { equipmentNumber: { [Op.iLike]: `%${search}%` } },
        { brand: { [Op.iLike]: `%${search}%` } },
        { model: { [Op.iLike]: `%${search}%` } },
        { serialNumber: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (category) {
      whereClause.category = category;
    }
    
    if (status) {
      whereClause.status = status;
    }

    // إعداد include للعملاء
    const includeOptions = [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'name', 'customer_code', 'phone'],
        required: false
      }
    ];

    // إضافة فلتر العميل إذا تم تحديده
    if (customer) {
      includeOptions[0].where = { 
        [Op.or]: [
          { name: { [Op.iLike]: `%${customer}%` } },
          { customer_code: { [Op.iLike]: `%${customer}%` } }
        ]
      };
      includeOptions[0].required = true;
    }

    const { count, rows: equipment } = await Equipment.findAndCountAll({
      where: whereClause,
      include: includeOptions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy === 'created_at' ? 'created_at' : sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        equipment,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all equipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch equipment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على معدة واحدة
const getEquipmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const equipment = await Equipment.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customer_code', 'phone', 'email']
        }
      ]
    });

    if (!equipment) {
      return res.status(404).json({
        success: false,
        message: 'Equipment not found'
      });
    }

    res.json({
      success: true,
      data: { equipment }
    });

  } catch (error) {
    console.error('Get equipment by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch equipment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء معدة جديدة
const createEquipment = async (req, res) => {
  try {
    const {
      equipmentNumber,
      name,
      description,
      category,
      brand,
      model,
      serialNumber,
      yearOfManufacture,
      purchaseDate,
      purchasePrice,
      currentValue,
      location,
      status = 'operational',
      condition = 'good',
      operatingHours = 0,
      maintenanceInterval,
      notes,
      customerId
    } = req.body;

    // التحقق من عدم تكرار رقم المعدة
    const existingEquipment = await Equipment.findOne({
      where: {
        [Op.or]: [
          { equipmentNumber },
          ...(serialNumber ? [{ serialNumber }] : [])
        ]
      }
    });

    if (existingEquipment) {
      return res.status(400).json({
        success: false,
        message: existingEquipment.equipmentNumber === equipmentNumber 
          ? 'Equipment number already exists' 
          : 'Serial number already exists'
      });
    }

    // التحقق من وجود العميل إذا تم تحديده
    if (customerId) {
      const customer = await Customer.findByPk(customerId);
      if (!customer) {
        return res.status(400).json({
          success: false,
          message: 'Customer not found'
        });
      }
    }

    // إنشاء المعدة
    const equipment = await Equipment.create({
      equipmentNumber,
      name,
      description,
      category,
      brand,
      model,
      serialNumber,
      yearOfManufacture,
      purchaseDate,
      purchasePrice,
      currentValue,
      location,
      status,
      condition,
      operatingHours,
      maintenanceInterval,
      notes,
      customerId
    });

    // إعادة جلب المعدة مع العميل
    const equipmentWithCustomer = await Equipment.findByPk(equipment.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customer_code', 'phone']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Equipment created successfully',
      data: { equipment: equipmentWithCustomer }
    });

  } catch (error) {
    console.error('Create equipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create equipment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث معدة
const updateEquipment = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const equipment = await Equipment.findByPk(id);
    if (!equipment) {
      return res.status(404).json({
        success: false,
        message: 'Equipment not found'
      });
    }

    // التحقق من عدم تكرار رقم المعدة أو الرقم التسلسلي
    if (updateData.equipmentNumber || updateData.serialNumber) {
      const whereConditions = [];
      
      if (updateData.equipmentNumber && updateData.equipmentNumber !== equipment.equipmentNumber) {
        whereConditions.push({ equipmentNumber: updateData.equipmentNumber });
      }
      
      if (updateData.serialNumber && updateData.serialNumber !== equipment.serialNumber) {
        whereConditions.push({ serialNumber: updateData.serialNumber });
      }

      if (whereConditions.length > 0) {
        const existingEquipment = await Equipment.findOne({
          where: {
            id: { [Op.ne]: id },
            [Op.or]: whereConditions
          }
        });

        if (existingEquipment) {
          return res.status(400).json({
            success: false,
            message: 'Equipment number or serial number already exists'
          });
        }
      }
    }

    // التحقق من وجود العميل إذا تم تحديده
    if (updateData.customerId) {
      const customer = await Customer.findByPk(updateData.customerId);
      if (!customer) {
        return res.status(400).json({
          success: false,
          message: 'Customer not found'
        });
      }
    }

    // تحديث المعدة
    await equipment.update(updateData);

    // إعادة جلب المعدة مع العميل
    const updatedEquipment = await Equipment.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customer_code', 'phone']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Equipment updated successfully',
      data: { equipment: updatedEquipment }
    });

  } catch (error) {
    console.error('Update equipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update equipment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف معدة
const deleteEquipment = async (req, res) => {
  try {
    const { id } = req.params;

    const equipment = await Equipment.findByPk(id);
    if (!equipment) {
      return res.status(404).json({
        success: false,
        message: 'Equipment not found'
      });
    }

    await equipment.destroy();

    res.json({
      success: true,
      message: 'Equipment deleted successfully'
    });

  } catch (error) {
    console.error('Delete equipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete equipment',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على إحصائيات المعدات
const getEquipmentStats = async (req, res) => {
  try {
    const totalEquipment = await Equipment.count();
    
    const statusStats = await Equipment.findAll({
      attributes: [
        'status',
        [Equipment.sequelize.fn('COUNT', Equipment.sequelize.col('id')), 'count']
      ],
      group: ['status']
    });

    const categoryStats = await Equipment.findAll({
      attributes: [
        'category',
        [Equipment.sequelize.fn('COUNT', Equipment.sequelize.col('id')), 'count']
      ],
      group: ['category']
    });

    const conditionStats = await Equipment.findAll({
      attributes: [
        'condition',
        [Equipment.sequelize.fn('COUNT', Equipment.sequelize.col('id')), 'count']
      ],
      group: ['condition']
    });

    // المعدات التي تحتاج صيانة
    const maintenanceDue = await Equipment.count({
      where: {
        nextMaintenanceDate: {
          [Op.lte]: new Date()
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalEquipment,
        maintenanceDue,
        statusStats: statusStats.reduce((acc, item) => {
          acc[item.status] = parseInt(item.dataValues.count);
          return acc;
        }, {}),
        categoryStats: categoryStats.reduce((acc, item) => {
          acc[item.category] = parseInt(item.dataValues.count);
          return acc;
        }, {}),
        conditionStats: conditionStats.reduce((acc, item) => {
          acc[item.condition] = parseInt(item.dataValues.count);
          return acc;
        }, {})
      }
    });

  } catch (error) {
    console.error('Get equipment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch equipment statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllEquipment,
  getEquipmentById,
  createEquipment,
  updateEquipment,
  deleteEquipment,
  getEquipmentStats
};

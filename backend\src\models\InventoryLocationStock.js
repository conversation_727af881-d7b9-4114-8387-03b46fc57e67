const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InventoryLocationStock = sequelize.define('InventoryLocationStock', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    itemId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'item_id',
      references: {
        model: 'inventory_items',
        key: 'id'
      }
    },
    locationId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'location_id',
      references: {
        model: 'inventory_locations',
        key: 'id'
      }
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    reservedQuantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
      field: 'reserved_quantity',
      validate: {
        min: 0
      }
    }
  }, {
    tableName: 'inventory_location_stock',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['item_id']
      },
      {
        fields: ['location_id']
      },
      {
        fields: ['item_id', 'location_id'],
        unique: true
      }
    ]
  });

  // Instance methods
  InventoryLocationStock.prototype.getAvailableQuantity = function() {
    return parseFloat(this.quantity) - parseFloat(this.reservedQuantity);
  };

  InventoryLocationStock.prototype.canReserve = function(requestedQuantity) {
    return this.getAvailableQuantity() >= parseFloat(requestedQuantity);
  };

  InventoryLocationStock.prototype.reserve = async function(quantity) {
    if (!this.canReserve(quantity)) {
      throw new Error('Insufficient available quantity to reserve');
    }
    
    this.reservedQuantity = parseFloat(this.reservedQuantity) + parseFloat(quantity);
    await this.save();
    
    return this;
  };

  InventoryLocationStock.prototype.releaseReservation = async function(quantity) {
    const releaseAmount = Math.min(parseFloat(quantity), parseFloat(this.reservedQuantity));
    this.reservedQuantity = parseFloat(this.reservedQuantity) - releaseAmount;
    await this.save();
    
    return this;
  };

  // Associations
  InventoryLocationStock.associate = function(models) {
    // InventoryLocationStock belongs to InventoryItem
    if (models.InventoryItem) {
      InventoryLocationStock.belongsTo(models.InventoryItem, {
        foreignKey: 'item_id',
        as: 'item'
      });
    }

    // InventoryLocationStock belongs to InventoryLocation
    if (models.InventoryLocation) {
      InventoryLocationStock.belongsTo(models.InventoryLocation, {
        foreignKey: 'location_id',
        as: 'location'
      });
    }
  };

  return InventoryLocationStock;
};

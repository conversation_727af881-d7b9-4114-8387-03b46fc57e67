const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPIs() {
  console.log('🧪 Testing APIs...\n');

  try {
    // 1. اختبار Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data);
    console.log('');

    // 2. اختبار تسجيل الدخول
    console.log('2️⃣ Testing Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!'
    });
    console.log('✅ Login successful!');
    console.log('User:', loginResponse.data.data.user.username);
    console.log('Roles:', loginResponse.data.data.user.roles.map(r => r.displayName).join(', '));
    console.log('Permissions count:', loginResponse.data.data.user.permissions.length);
    
    const token = loginResponse.data.data.token;
    console.log('Token received ✅\n');

    // 3. اختبار الحصول على الملف الشخصي
    console.log('3️⃣ Testing Get Profile...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Profile retrieved!');
    console.log('User:', profileResponse.data.data.user.firstName, profileResponse.data.data.user.lastName);
    console.log('');

    // 4. اختبار التحقق من التوكن
    console.log('4️⃣ Testing Token Verification...');
    const verifyResponse = await axios.get(`${BASE_URL}/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Token verified!');
    console.log('User ID:', verifyResponse.data.data.userId);
    console.log('Permissions count:', verifyResponse.data.data.permissions.length);
    console.log('');

    // 5. اختبار الحصول على المستخدمين
    console.log('5️⃣ Testing Get Users...');
    const usersResponse = await axios.get(`${BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Users retrieved!');
    console.log('Total users:', usersResponse.data.data.pagination.totalItems);
    console.log('');

    // 6. اختبار الحصول على الأدوار
    console.log('6️⃣ Testing Get Roles...');
    const rolesResponse = await axios.get(`${BASE_URL}/roles`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Roles retrieved!');
    console.log('Total roles:', rolesResponse.data.data.pagination.totalItems);
    rolesResponse.data.data.roles.forEach(role => {
      console.log(`  - ${role.displayName} (${role.name})`);
    });
    console.log('');

    // 7. اختبار الحصول على الصلاحيات
    console.log('7️⃣ Testing Get Permissions...');
    const permissionsResponse = await axios.get(`${BASE_URL}/roles/permissions/all`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Permissions retrieved!');
    console.log('Total permissions:', permissionsResponse.data.data.permissions.length);
    
    // تجميع الصلاحيات حسب الوحدة
    const permissionsByModule = {};
    permissionsResponse.data.data.permissions.forEach(permission => {
      if (!permissionsByModule[permission.module]) {
        permissionsByModule[permission.module] = [];
      }
      permissionsByModule[permission.module].push(permission.action);
    });
    
    console.log('Permissions by module:');
    Object.keys(permissionsByModule).forEach(module => {
      console.log(`  - ${module}: ${permissionsByModule[module].join(', ')}`);
    });

    console.log('\n🎉 All tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data?.errors) {
      console.error('Validation errors:', error.response.data.errors);
    }
  }
}

// تشغيل الاختبارات
testAPIs();

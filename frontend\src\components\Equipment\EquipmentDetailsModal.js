import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import {
  getEquipmentCategoryLabel,
  getEquipmentStatusLabel,
  getEquipmentConditionLabel,
  getStatusColor,
  getConditionColor,
  formatCurrency,
  formatOperatingHours
} from '../../services/equipmentService';

const EquipmentDetailsModal = ({ isOpen, onClose, equipment }) => {
  if (!isOpen || !equipment) return null;

  const getStatusBadgeClass = (status) => {
    const color = getStatusColor(status);
    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      red: 'bg-red-100 text-red-800',
      gray: 'bg-gray-100 text-gray-800',
      blue: 'bg-blue-100 text-blue-800'
    };
    return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color] || colorClasses.gray}`;
  };

  const getConditionBadgeClass = (condition) => {
    const color = getConditionColor(condition);
    const colorClasses = {
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      red: 'bg-red-100 text-red-800',
      gray: 'bg-gray-100 text-gray-800',
      blue: 'bg-blue-100 text-blue-800'
    };
    return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[color] || colorClasses.gray}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900">
            تفاصيل المعدة
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-6">
          {/* المعلومات الأساسية */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">رقم المعدة</label>
                <p className="mt-1 text-sm text-gray-900 font-medium">{equipment.equipmentNumber || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">اسم المعدة</label>
                <p className="mt-1 text-sm text-gray-900 font-medium">{equipment.name || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الفئة</label>
                <p className="mt-1 text-sm text-gray-900">{getEquipmentCategoryLabel(equipment.category)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الماركة</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.brand || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الموديل</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.model || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الرقم التسلسلي</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.serialNumber || 'غير محدد'}</p>
              </div>
            </div>
          </div>

          {/* الحالة والموقع */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">الحالة والموقع</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">الحالة</label>
                <div className="mt-1">
                  <span className={getStatusBadgeClass(equipment.status)}>
                    {getEquipmentStatusLabel(equipment.status)}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الحالة العامة</label>
                <div className="mt-1">
                  <span className={getConditionBadgeClass(equipment.condition)}>
                    {getEquipmentConditionLabel(equipment.condition)}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الموقع</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.location || 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">ساعات التشغيل</label>
                <p className="mt-1 text-sm text-gray-900 font-medium">{formatOperatingHours(equipment.operatingHours)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">فترة الصيانة</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.maintenanceInterval ? `${equipment.maintenanceInterval} ساعة` : 'غير محدد'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">سنة الصنع</label>
                <p className="mt-1 text-sm text-gray-900">{equipment.yearOfManufacture || 'غير محدد'}</p>
              </div>
            </div>
          </div>

          {/* المعلومات المالية */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">المعلومات المالية</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">تاريخ الشراء</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(equipment.purchaseDate)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">سعر الشراء</label>
                <p className="mt-1 text-sm text-gray-900 font-medium">{formatCurrency(equipment.purchasePrice)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">القيمة الحالية</label>
                <p className="mt-1 text-sm text-gray-900 font-medium">{formatCurrency(equipment.currentValue)}</p>
              </div>
            </div>
          </div>

          {/* معلومات الصيانة */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">معلومات الصيانة</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">آخر صيانة</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(equipment.lastMaintenanceDate)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">الصيانة القادمة</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(equipment.nextMaintenanceDate)}</p>
              </div>
            </div>
          </div>

          {/* العميل */}
          {equipment.customer && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">معلومات العميل</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">اسم العميل</label>
                  <p className="mt-1 text-sm text-gray-900 font-medium">{equipment.customer.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">رمز العميل</label>
                  <p className="mt-1 text-sm text-gray-900">{equipment.customer.customerCode}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">رقم الهاتف</label>
                  <p className="mt-1 text-sm text-gray-900">{equipment.customer.phone || 'غير محدد'}</p>
                </div>
              </div>
            </div>
          )}

          {/* الوصف والملاحظات */}
          {(equipment.description || equipment.notes) && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">الوصف والملاحظات</h4>
              {equipment.description && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-500 mb-2">الوصف</label>
                  <p className="text-sm text-gray-900 bg-white p-3 rounded border">{equipment.description}</p>
                </div>
              )}
              {equipment.notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">الملاحظات</label>
                  <p className="text-sm text-gray-900 bg-white p-3 rounded border">{equipment.notes}</p>
                </div>
              )}
            </div>
          )}

          {/* معلومات النظام */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">معلومات النظام</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">تاريخ الإنشاء</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(equipment.createdAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">آخر تحديث</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(equipment.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* زر الإغلاق */}
        <div className="flex justify-end pt-6">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default EquipmentDetailsModal;

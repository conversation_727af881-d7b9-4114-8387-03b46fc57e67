{"ast": null, "code": "import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};", "map": {"version": 3, "names": ["setDefaults", "setI18n", "initReactI18next", "type", "init", "instance", "options", "react"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/node_modules/react-i18next/dist/es/initReactI18next.js"], "sourcesContent": ["import { setDefaults } from './defaults.js';\nimport { setI18n } from './i18nInstance.js';\nexport const initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    setDefaults(instance.options.react);\n    setI18n(instance);\n  }\n};"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAACC,QAAQ,EAAE;IACbL,WAAW,CAACK,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC;IACnCN,OAAO,CAACI,QAAQ,CAAC;EACnB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
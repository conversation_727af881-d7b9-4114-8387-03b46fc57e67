const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkOrder = sequelize.define('WorkOrder', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    workOrderNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'work_order_number',
      validate: {
        len: [5, 50],
        notEmpty: true
      }
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [5, 200],
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM,
      values: ['preventive', 'corrective', 'emergency', 'inspection', 'overhaul'],
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    priority: {
      type: DataTypes.ENUM,
      values: ['low', 'medium', 'high', 'critical'],
      defaultValue: 'medium',
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM,
      values: ['draft', 'pending', 'in_progress', 'on_hold', 'completed', 'cancelled'],
      defaultValue: 'draft',
      allowNull: false
    },
    requestedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'requested_date',
      defaultValue: DataTypes.NOW
    },
    scheduledStartDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'scheduled_start_date'
    },
    scheduledEndDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'scheduled_end_date'
    },
    actualStartDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'actual_start_date'
    },
    actualEndDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'actual_end_date'
    },
    estimatedHours: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      field: 'estimated_hours',
      validate: {
        min: 0
      }
    },
    actualHours: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      field: 'actual_hours',
      validate: {
        min: 0
      }
    },
    laborCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'labor_cost',
      validate: {
        min: 0
      }
    },
    partsCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'parts_cost',
      validate: {
        min: 0
      }
    },
    otherCosts: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'other_costs',
      validate: {
        min: 0
      }
    },
    totalCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'total_cost',
      validate: {
        min: 0
      }
    },
    failureDescription: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'failure_description'
    },
    rootCause: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'root_cause'
    },
    actionTaken: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'action_taken'
    },
    preventiveAction: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'preventive_action'
    },
    completionNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'completion_notes'
    },
    customerSatisfaction: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'customer_satisfaction',
      validate: {
        min: 1,
        max: 5
      }
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    isWarranty: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_warranty'
    },
    warrantyDetails: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'warranty_details'
    }
  }, {
    tableName: 'work_orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['work_order_number']
      },
      {
        fields: ['equipment_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['type']
      },
      {
        fields: ['assigned_to']
      },
      {
        fields: ['scheduled_start_date']
      }
    ]
  });

  // Instance methods
  WorkOrder.prototype.isOverdue = function() {
    if (!this.scheduledEndDate || this.status === 'completed' || this.status === 'cancelled') {
      return false;
    }
    return new Date() > new Date(this.scheduledEndDate);
  };

  WorkOrder.prototype.getDuration = function() {
    if (!this.actualStartDate || !this.actualEndDate) return null;
    const start = new Date(this.actualStartDate);
    const end = new Date(this.actualEndDate);
    return Math.ceil((end - start) / (1000 * 60 * 60)); // Duration in hours
  };

  WorkOrder.prototype.calculateTotalCost = function() {
    return parseFloat(this.laborCost) + parseFloat(this.partsCost) + parseFloat(this.otherCosts);
  };

  // Associations
  WorkOrder.associate = function(models) {
    // WorkOrder belongs to Equipment
    WorkOrder.belongsTo(models.Equipment, {
      foreignKey: 'equipment_id',
      as: 'equipment'
    });

    // WorkOrder belongs to User (assigned to)
    WorkOrder.belongsTo(models.User, {
      foreignKey: 'assigned_to',
      as: 'assignedUser'
    });

    // WorkOrder belongs to User (created by)
    WorkOrder.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });

    // WorkOrder has many WorkOrderParts
    WorkOrder.hasMany(models.WorkOrderPart, {
      foreignKey: 'work_order_id',
      as: 'parts'
    });

    // WorkOrder has many WorkOrderTasks
    WorkOrder.hasMany(models.WorkOrderTask, {
      foreignKey: 'work_order_id',
      as: 'tasks'
    });
  };

  return WorkOrder;
};

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkOrder = sequelize.define('WorkOrder', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    workOrderNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'work_order_number',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    customerId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'customer_id',
      references: {
        model: 'customers',
        key: 'id'
      }
    },
    equipmentId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'equipment_id',
      references: {
        model: 'equipment',
        key: 'id'
      }
    },
    // معلومات الاستلام
    receivedDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'received_date',
      defaultValue: DataTypes.NOW
    },
    receivedBy: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'received_by',
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    // وصف المشكلة
    problemDescription: {
      type: DataTypes.TEXT,
      allowNull: false,
      field: 'problem_description',
      validate: {
        notEmpty: true
      }
    },
    customerComplaint: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'customer_complaint'
    },
    // الفحص الأولي
    initialDiagnosis: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'initial_diagnosis'
    },
    estimatedCost: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'estimated_cost',
      validate: {
        min: 0
      }
    },
    estimatedCompletionDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'estimated_completion_date'
    },
    // حالة أمر العمل
    status: {
      type: DataTypes.ENUM(
        'received',           // مستلم
        'diagnosed',          // تم التشخيص
        'approved',           // موافق عليه
        'in_progress',        // قيد التنفيذ
        'waiting_parts',      // انتظار قطع غيار
        'testing',            // اختبار
        'completed',          // مكتمل
        'delivered',          // مسلم
        'cancelled'           // ملغي
      ),
      defaultValue: 'received',
      allowNull: false
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium',
      allowNull: false
    },
    // التكاليف
    laborCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'labor_cost',
      validate: {
        min: 0
      }
    },
    partsCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'parts_cost',
      validate: {
        min: 0
      }
    },
    otherCosts: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'other_costs',
      validate: {
        min: 0
      }
    },
    totalCost: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'total_cost',
      validate: {
        min: 0
      }
    },
    // التواريخ
    startedDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'started_date'
    },
    completedDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'completed_date'
    },
    deliveredDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'delivered_date'
    },
    // الفني المسؤول
    assignedTechnician: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'assigned_technician',
      validate: {
        len: [2, 100]
      }
    },
    // ملاحظات
    workPerformed: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'work_performed'
    },
    internalNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'internal_notes'
    },
    customerNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'customer_notes'
    },
    // معلومات الضمان
    warrantyPeriod: {
      type: DataTypes.INTEGER, // بالأيام
      allowNull: true,
      field: 'warranty_period',
      validate: {
        min: 0
      }
    },
    warrantyDescription: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'warranty_description'
    },
    // الصور والمرفقات
    beforeImages: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      field: 'before_images'
    },
    afterImages: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      field: 'after_images'
    },
    documents: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      field: 'documents'
    }
  }, {
    tableName: 'work_orders',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['work_order_number']
      },
      {
        fields: ['customer_id']
      },
      {
        fields: ['equipment_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['received_date']
      },
      {
        fields: ['assigned_technician']
      }
    ]
  });

  // Instance methods
  WorkOrder.prototype.calculateTotalCost = function() {
    const labor = parseFloat(this.laborCost) || 0;
    const parts = parseFloat(this.partsCost) || 0;
    const other = parseFloat(this.otherCosts) || 0;
    return labor + parts + other;
  };

  WorkOrder.prototype.isOverdue = function() {
    if (!this.estimatedCompletionDate) return false;
    const today = new Date();
    const completionDate = new Date(this.estimatedCompletionDate);
    return today > completionDate && !['completed', 'delivered', 'cancelled'].includes(this.status);
  };

  WorkOrder.prototype.getDaysInWorkshop = function() {
    const today = new Date();
    const receivedDate = new Date(this.receivedDate);
    const diffTime = Math.abs(today - receivedDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Associations
  WorkOrder.associate = function(models) {
    // WorkOrder belongs to Customer
    WorkOrder.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer'
    });

    // WorkOrder belongs to Equipment
    WorkOrder.belongsTo(models.Equipment, {
      foreignKey: 'equipment_id',
      as: 'equipment'
    });

    // WorkOrder has many WorkOrderParts
    if (models.WorkOrderPart) {
      WorkOrder.hasMany(models.WorkOrderPart, {
        foreignKey: 'work_order_id',
        as: 'parts'
      });
    }

    // WorkOrder has many WorkLogs
    if (models.WorkLog) {
      WorkOrder.hasMany(models.WorkLog, {
        foreignKey: 'work_order_id',
        as: 'workLogs'
      });
    }
  };

  return WorkOrder;
};

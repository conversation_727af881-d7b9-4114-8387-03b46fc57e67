const {
  InventoryItem,
  InventoryCategory,
  UnitOfMeasure,
  InventoryLocation,
  InventoryLocationStock,
  InventoryTransaction,
  sequelize
} = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع عناصر المخزون
const getAllInventoryItems = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      category = '',
      lowStock = false,
      outOfStock = false,
      sortBy = 'name',
      sortOrder = 'ASC'
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {
      isActive: true
    };

    // البحث
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { itemCode: { [Op.iLike]: `%${search}%` } },
        { barcode: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // فلترة حسب الفئة
    if (category) {
      whereClause.categoryId = category;
    }

    // فلترة المخزون المنخفض
    if (lowStock === 'true') {
      whereClause[Op.and] = [
        sequelize.where(sequelize.col('current_stock'), Op.lte, sequelize.col('minimum_stock')),
        { currentStock: { [Op.gt]: 0 } }
      ];
    }

    // فلترة المخزون المنتهي
    if (outOfStock === 'true') {
      whereClause.currentStock = { [Op.lte]: 0 };
    }

    const { count, rows } = await InventoryItem.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name', 'nameAr']
        },
        {
          model: UnitOfMeasure,
          as: 'unit',
          attributes: ['id', 'name', 'abbreviation']
        }
      ],
      limit: parseInt(limit),
      offset: offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        items: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get inventory items error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch inventory items',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على تفاصيل عنصر مخزون
const getInventoryItemById = async (req, res) => {
  try {
    const { id } = req.params;

    const item = await InventoryItem.findByPk(id, {
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name', 'nameAr', 'description']
        },
        {
          model: UnitOfMeasure,
          as: 'unit',
          attributes: ['id', 'name', 'abbreviation', 'type']
        },
        {
          model: InventoryTransaction,
          as: 'transactions',
          limit: 10,
          order: [['created_at', 'DESC']],
          attributes: ['id', 'type', 'quantity', 'unitCost', 'totalCost', 'reference', 'notes', 'created_at']
        }
      ]
    });

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    res.json({
      success: true,
      data: item
    });

  } catch (error) {
    console.error('Get inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch inventory item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء عنصر مخزون جديد
const createInventoryItem = async (req, res) => {
  try {
    const {
      itemCode,
      name,
      description,
      categoryId,
      unitId,
      minimumStock,
      maximumStock,
      reorderLevel,
      reorderQuantity,
      unitCost,
      sellingPrice,
      location,
      shelf,
      barcode,
      supplier,
      leadTime,
      specifications,
      images
    } = req.body;

    // التحقق من عدم تكرار رمز العنصر
    if (itemCode) {
      const existingItem = await InventoryItem.findOne({
        where: { itemCode }
      });

      if (existingItem) {
        return res.status(400).json({
          success: false,
          message: 'Item code already exists'
        });
      }
    }

    // التحقق من عدم تكرار الباركود
    if (barcode) {
      const existingBarcode = await InventoryItem.findOne({
        where: { barcode }
      });

      if (existingBarcode) {
        return res.status(400).json({
          success: false,
          message: 'Barcode already exists'
        });
      }
    }

    const item = await InventoryItem.create({
      itemCode,
      name,
      description,
      categoryId,
      unitId,
      minimumStock: minimumStock || 0,
      maximumStock,
      reorderLevel,
      reorderQuantity,
      unitCost: unitCost || 0,
      sellingPrice,
      location,
      shelf,
      barcode,
      supplier,
      leadTime,
      specifications: specifications || {},
      images: images || []
    });

    // جلب العنصر مع العلاقات
    const createdItem = await InventoryItem.findByPk(item.id, {
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name', 'nameAr']
        },
        {
          model: UnitOfMeasure,
          as: 'unit',
          attributes: ['id', 'name', 'abbreviation']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Inventory item created successfully',
      data: createdItem
    });

  } catch (error) {
    console.error('Create inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create inventory item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث عنصر مخزون
const updateInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // التحقق من عدم تكرار رمز العنصر
    if (updateData.itemCode && updateData.itemCode !== item.itemCode) {
      const existingItem = await InventoryItem.findOne({
        where: { 
          itemCode: updateData.itemCode,
          id: { [Op.ne]: id }
        }
      });

      if (existingItem) {
        return res.status(400).json({
          success: false,
          message: 'Item code already exists'
        });
      }
    }

    // التحقق من عدم تكرار الباركود
    if (updateData.barcode && updateData.barcode !== item.barcode) {
      const existingBarcode = await InventoryItem.findOne({
        where: { 
          barcode: updateData.barcode,
          id: { [Op.ne]: id }
        }
      });

      if (existingBarcode) {
        return res.status(400).json({
          success: false,
          message: 'Barcode already exists'
        });
      }
    }

    await item.update(updateData);

    // جلب العنصر المحدث مع العلاقات
    const updatedItem = await InventoryItem.findByPk(id, {
      include: [
        {
          model: InventoryCategory,
          as: 'category',
          attributes: ['id', 'name', 'nameAr']
        },
        {
          model: UnitOfMeasure,
          as: 'unit',
          attributes: ['id', 'name', 'abbreviation']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Inventory item updated successfully',
      data: updatedItem
    });

  } catch (error) {
    console.error('Update inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update inventory item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف عنصر مخزون
const deleteInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    // التحقق من وجود معاملات مرتبطة
    const transactionCount = await InventoryTransaction.count({
      where: { itemId: id }
    });

    if (transactionCount > 0) {
      // إلغاء تفعيل بدلاً من الحذف
      await item.update({ isActive: false });

      res.json({
        success: true,
        message: 'Inventory item deactivated successfully (has related transactions)'
      });
    } else {
      await item.destroy();

      res.json({
        success: true,
        message: 'Inventory item deleted successfully'
      });
    }

  } catch (error) {
    console.error('Delete inventory item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete inventory item',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على إحصائيات المخزون
const getInventoryStats = async (req, res) => {
  try {
    const totalItems = await InventoryItem.count({
      where: { isActive: true }
    });

    const lowStockItems = await InventoryItem.count({
      where: {
        isActive: true,
        [Op.and]: [
          sequelize.where(sequelize.col('current_stock'), Op.lte, sequelize.col('minimum_stock')),
          { currentStock: { [Op.gt]: 0 } }
        ]
      }
    });

    const outOfStockItems = await InventoryItem.count({
      where: {
        isActive: true,
        currentStock: { [Op.lte]: 0 }
      }
    });

    // حساب القيمة الإجمالية للمخزون
    const totalValueResult = await InventoryItem.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.literal('current_stock * unit_cost')), 'totalValue']
      ],
      where: { isActive: true },
      raw: true
    });

    const totalValue = totalValueResult[0]?.totalValue || 0;

    // إحصائيات حسب الفئة (مبسطة)
    const categoryStats = [];

    res.json({
      success: true,
      data: {
        totalItems,
        lowStockItems,
        outOfStockItems,
        totalValue: totalValue || 0,
        categoryStats
      }
    });

  } catch (error) {
    console.error('Get inventory stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch inventory statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث مخزون عنصر
const updateItemStock = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity, type, unitCost, reference, notes } = req.body;

    if (!quantity || !type) {
      return res.status(400).json({
        success: false,
        message: 'Quantity and type are required'
      });
    }

    const item = await InventoryItem.findByPk(id);

    if (!item) {
      return res.status(404).json({
        success: false,
        message: 'Inventory item not found'
      });
    }

    const transaction = await sequelize.transaction();

    try {
      // تحديث المخزون
      let newStock = parseFloat(item.currentStock);

      if (type === 'in') {
        newStock += parseFloat(quantity);
      } else if (type === 'out') {
        if (newStock < parseFloat(quantity)) {
          throw new Error('Insufficient stock');
        }
        newStock -= parseFloat(quantity);
      } else {
        throw new Error('Invalid transaction type');
      }

      await item.update({ currentStock: newStock }, { transaction });

      // إنشاء معاملة مخزون
      await InventoryTransaction.create({
        itemId: id,
        type,
        quantity: parseFloat(quantity),
        unitCost: unitCost || item.unitCost,
        totalCost: parseFloat(quantity) * (unitCost || item.unitCost),
        reference,
        notes,
        balanceAfter: newStock
      }, { transaction });

      await transaction.commit();

      // جلب العنصر المحدث
      const updatedItem = await InventoryItem.findByPk(id, {
        include: [
          {
            model: InventoryCategory,
            as: 'category',
            attributes: ['id', 'name', 'nameAr']
          },
          {
            model: UnitOfMeasure,
            as: 'unit',
            attributes: ['id', 'name', 'abbreviation']
          }
        ]
      });

      res.json({
        success: true,
        message: 'Stock updated successfully',
        data: updatedItem
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('Update stock error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update stock',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllInventoryItems,
  getInventoryItemById,
  createInventoryItem,
  updateInventoryItem,
  deleteInventoryItem,
  getInventoryStats,
  updateItemStock
};

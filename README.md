# نظام ERP لورشة صيانة المعدات الثقيلة

## نظرة عامة
نظام إدارة موارد المؤسسة (ERP) مخصص لورش صيانة المعدات الثقيلة، يشمل إدارة المخازن، الصيانة، الحسابات، والتقارير.

## المميزات الرئيسية
- 🔧 إدارة المعدات وأوامر الصيانة
- 🏪 إدارة المخازن مع تنبيهات انخفاض المخزون
- 💰 نظام الحسابات والفواتير
- 📊 تقارير تفاعلية مع رسومات بيانية
- 📥📤 تصدير واستيراد Excel
- 🔐 نظام صلاحيات متقدم (RBAC)
- 🌐 دعم اللغة العربية

## التقنيات المستخدمة
### الباك إند
- Node.js + Express.js
- PostgreSQL + Sequelize
- JWT للمصادقة
- ExcelJS لمعالجة ملفات Excel

### الفرونت إند
- React.js
- Tailwind CSS
- Redux Toolkit
- Chart.js للرسومات البيانية

## هيكل المشروع
```
heavy-equipment-erp/
├── backend/                 # الباك إند
│   ├── src/
│   │   ├── controllers/     # المتحكمات
│   │   ├── models/          # نماذج قاعدة البيانات
│   │   ├── routes/          # المسارات
│   │   ├── middleware/      # الوسطاء
│   │   ├── services/        # الخدمات
│   │   └── utils/           # الأدوات المساعدة
│   ├── config/              # إعدادات قاعدة البيانات
│   └── package.json
├── frontend/                # الفرونت إند
│   ├── src/
│   │   ├── components/      # المكونات
│   │   ├── pages/           # الصفحات
│   │   ├── store/           # Redux store
│   │   ├── services/        # خدمات API
│   │   └── utils/           # الأدوات المساعدة
│   └── package.json
└── README.md
```

## التثبيت والتشغيل
### الباك إند
```bash
cd backend
npm install
npm run dev
```

### الفرونت إند
```bash
cd frontend
npm install
npm start
```

## الوحدات الرئيسية
1. **وحدة المصادقة والصلاحيات**
2. **وحدة إدارة المعدات والصيانة**
3. **وحدة إدارة المخازن**
4. **وحدة الحسابات والفواتير**
5. **وحدة التقارير**
6. **وحدة تصدير/استيراد Excel**

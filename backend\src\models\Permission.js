const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Permission = sequelize.define('Permission', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    displayName: {
      type: DataTypes.STRING(150),
      allowNull: false,
      field: 'display_name',
      validate: {
        len: [2, 150],
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    module: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [2, 50],
        notEmpty: true,
        isIn: [['users', 'equipment', 'inventory', 'maintenance', 'accounting', 'reports', 'settings']]
      }
    },
    action: {
      type: DataTypes.STRING(20),
      allowNull: false,
      validate: {
        len: [2, 20],
        notEmpty: true,
        isIn: [['create', 'read', 'update', 'delete', 'export', 'import', 'approve']]
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'permissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['module', 'action']
      }
    ]
  });

  // Associations
  Permission.associate = function(models) {
    // Permission belongs to many Roles through RolePermissions
    Permission.belongsToMany(models.Role, {
      through: 'RolePermissions',
      foreignKey: 'permission_id',
      otherKey: 'role_id',
      as: 'roles'
    });
  };

  return Permission;
};

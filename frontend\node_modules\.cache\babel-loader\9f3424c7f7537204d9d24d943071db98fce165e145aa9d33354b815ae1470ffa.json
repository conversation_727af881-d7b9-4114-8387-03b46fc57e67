{"ast": null, "code": "import api from './api';\nconst authService = {\n  // تسجيل الدخول\n  login: async (email, password) => {\n    const response = await api.post('/auth/login', {\n      email,\n      password\n    });\n    return response.data;\n  },\n  // التسجيل\n  register: async userData => {\n    const response = await api.post('/auth/register', userData);\n    return response.data;\n  },\n  // تسجيل الخروج\n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n  // الحصول على الملف الشخصي\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data;\n  },\n  // تحديث الملف الشخصي\n  updateProfile: async userData => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data;\n  },\n  // تغيير كلمة المرور\n  changePassword: async passwordData => {\n    const response = await api.put('/auth/change-password', passwordData);\n    return response.data;\n  },\n  // التحقق من صحة التوكن\n  verifyToken: async () => {\n    const response = await api.get('/auth/verify');\n    return response.data;\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["api", "authService", "login", "email", "password", "response", "post", "data", "register", "userData", "logout", "getProfile", "get", "updateProfile", "put", "changePassword", "passwordData", "verifyToken"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/services/authService.js"], "sourcesContent": ["import api from './api';\n\nconst authService = {\n  // تسجيل الدخول\n  login: async (email, password) => {\n    const response = await api.post('/auth/login', { email, password });\n    return response.data;\n  },\n\n  // التسجيل\n  register: async (userData) => {\n    const response = await api.post('/auth/register', userData);\n    return response.data;\n  },\n\n  // تسجيل الخروج\n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n\n  // الحصول على الملف الشخصي\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data;\n  },\n\n  // تحديث الملف الشخصي\n  updateProfile: async (userData) => {\n    const response = await api.put('/auth/profile', userData);\n    return response.data;\n  },\n\n  // تغيير كلمة المرور\n  changePassword: async (passwordData) => {\n    const response = await api.put('/auth/change-password', passwordData);\n    return response.data;\n  },\n\n  // التحقق من صحة التوكن\n  verifyToken: async () => {\n    const response = await api.get('/auth/verify');\n    return response.data;\n  },\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAChC,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,aAAa,EAAE;MAAEH,KAAK;MAAEC;IAAS,CAAC,CAAC;IACnE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,gBAAgB,EAAEG,QAAQ,CAAC;IAC3D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAML,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,cAAc,CAAC;IAC/C,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAI,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,MAAMN,QAAQ,GAAG,MAAML,GAAG,CAACY,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,aAAa,EAAE,MAAOJ,QAAQ,IAAK;IACjC,MAAMJ,QAAQ,GAAG,MAAML,GAAG,CAACc,GAAG,CAAC,eAAe,EAAEL,QAAQ,CAAC;IACzD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAQ,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,MAAMX,QAAQ,GAAG,MAAML,GAAG,CAACc,GAAG,CAAC,uBAAuB,EAAEE,YAAY,CAAC;IACrE,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,MAAMZ,QAAQ,GAAG,MAAML,GAAG,CAACY,GAAG,CAAC,cAAc,CAAC;IAC9C,OAAOP,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
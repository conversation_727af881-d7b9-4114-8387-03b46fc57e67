import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { deleteEquipment, selectEquipmentLoading } from '../../store/slices/equipmentSlice';
import toast from 'react-hot-toast';

const DeleteConfirmModal = ({ isOpen, onClose, equipment, onSuccess }) => {
  const dispatch = useDispatch();
  const loading = useSelector(selectEquipmentLoading);

  const handleDelete = async () => {
    try {
      await dispatch(deleteEquipment(equipment.id)).unwrap();
      toast.success('تم حذف المعدة بنجاح');
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      toast.error(error || 'حدث خطأ أثناء حذف المعدة');
    }
  };

  if (!isOpen || !equipment) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3 text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">
            تأكيد حذف المعدة
          </h3>
          <div className="mt-2 px-7 py-3">
            <p className="text-sm text-gray-500">
              هل أنت متأكد من حذف المعدة "{equipment.name}"؟
            </p>
            <p className="text-sm text-gray-500 mt-2">
              رقم المعدة: {equipment.equipmentNumber}
            </p>
            <p className="text-xs text-red-500 mt-3">
              هذا الإجراء لا يمكن التراجع عنه.
            </p>
          </div>
          <div className="flex justify-center space-x-3 space-x-reverse px-4 py-3">
            <button
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              إلغاء
            </button>
            <button
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'جاري الحذف...' : 'حذف'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmModal;

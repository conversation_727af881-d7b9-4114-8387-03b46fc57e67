{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Fady\\\\frontend\\\\src\\\\components\\\\Equipment\\\\EquipmentModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport { createEquipment, updateEquipment, fetchCustomersForSelect, selectCustomersForSelect, selectEquipmentLoading } from '../../store/slices/equipmentSlice';\nimport { equipmentCategories, equipmentStatuses, equipmentConditions } from '../../services/equipmentService';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EquipmentModal = ({\n  isOpen,\n  onClose,\n  equipment = null,\n  onSuccess\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const customersForSelect = useSelector(selectCustomersForSelect);\n  const loading = useSelector(selectEquipmentLoading);\n  const [formData, setFormData] = useState({\n    equipmentNumber: '',\n    name: '',\n    description: '',\n    category: 'excavator',\n    brand: '',\n    model: '',\n    serialNumber: '',\n    yearOfManufacture: '',\n    purchaseDate: '',\n    purchasePrice: '',\n    currentValue: '',\n    location: '',\n    status: 'operational',\n    condition: 'good',\n    operatingHours: 0,\n    maintenanceInterval: '',\n    notes: '',\n    customerId: ''\n  });\n  const [errors, setErrors] = useState({});\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(fetchCustomersForSelect());\n      if (equipment) {\n        // تعديل معدة موجودة\n        setFormData({\n          equipmentNumber: equipment.equipmentNumber || '',\n          name: equipment.name || '',\n          description: equipment.description || '',\n          category: equipment.category || 'excavator',\n          brand: equipment.brand || '',\n          model: equipment.model || '',\n          serialNumber: equipment.serialNumber || '',\n          yearOfManufacture: equipment.yearOfManufacture || '',\n          purchaseDate: equipment.purchaseDate || '',\n          purchasePrice: equipment.purchasePrice || '',\n          currentValue: equipment.currentValue || '',\n          location: equipment.location || '',\n          status: equipment.status || 'operational',\n          condition: equipment.condition || 'good',\n          operatingHours: equipment.operatingHours || 0,\n          maintenanceInterval: equipment.maintenanceInterval || '',\n          notes: equipment.notes || '',\n          customerId: equipment.customerId || ''\n        });\n      } else {\n        // إضافة معدة جديدة\n        setFormData({\n          equipmentNumber: '',\n          name: '',\n          description: '',\n          category: 'excavator',\n          brand: '',\n          model: '',\n          serialNumber: '',\n          yearOfManufacture: '',\n          purchaseDate: '',\n          purchasePrice: '',\n          currentValue: '',\n          location: '',\n          status: 'operational',\n          condition: 'good',\n          operatingHours: 0,\n          maintenanceInterval: '',\n          notes: '',\n          customerId: ''\n        });\n      }\n      setErrors({});\n    }\n  }, [isOpen, equipment, dispatch]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // مسح الخطأ عند التعديل\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.equipmentNumber.trim()) {\n      newErrors.equipmentNumber = 'رقم المعدة مطلوب';\n    }\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المعدة مطلوب';\n    }\n    if (!formData.category) {\n      newErrors.category = 'فئة المعدة مطلوبة';\n    }\n    if (formData.yearOfManufacture && (formData.yearOfManufacture < 1900 || formData.yearOfManufacture > new Date().getFullYear())) {\n      newErrors.yearOfManufacture = 'سنة الصنع غير صحيحة';\n    }\n    if (formData.purchasePrice && formData.purchasePrice < 0) {\n      newErrors.purchasePrice = 'سعر الشراء لا يمكن أن يكون سالباً';\n    }\n    if (formData.currentValue && formData.currentValue < 0) {\n      newErrors.currentValue = 'القيمة الحالية لا يمكن أن تكون سالبة';\n    }\n    if (formData.operatingHours < 0) {\n      newErrors.operatingHours = 'ساعات التشغيل لا يمكن أن تكون سالبة';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      const submitData = {\n        ...formData,\n        yearOfManufacture: formData.yearOfManufacture ? parseInt(formData.yearOfManufacture) : null,\n        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : null,\n        currentValue: formData.currentValue ? parseFloat(formData.currentValue) : null,\n        operatingHours: parseInt(formData.operatingHours) || 0,\n        maintenanceInterval: formData.maintenanceInterval ? parseInt(formData.maintenanceInterval) : null,\n        customerId: formData.customerId || null\n      };\n      if (equipment) {\n        // تحديث معدة موجودة\n        await dispatch(updateEquipment({\n          id: equipment.id,\n          data: submitData\n        })).unwrap();\n        toast.success('تم تحديث المعدة بنجاح');\n      } else {\n        // إضافة معدة جديدة\n        await dispatch(createEquipment(submitData)).unwrap();\n        toast.success('تم إضافة المعدة بنجاح');\n      }\n      onSuccess && onSuccess();\n      onClose();\n    } catch (error) {\n      toast.error(error || 'حدث خطأ أثناء حفظ المعدة');\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: equipment ? 'تعديل المعدة' : 'إضافة معدة جديدة'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0629 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"equipmentNumber\",\n              value: formData.equipmentNumber,\n              onChange: handleChange,\n              className: `input-field ${errors.equipmentNumber ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: CAT001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), errors.equipmentNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.equipmentNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0639\\u062F\\u0629 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              value: formData.name,\n              onChange: handleChange,\n              className: `input-field ${errors.name ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: \\u062D\\u0641\\u0627\\u0631\\u0629 \\u0643\\u0627\\u062A\\u0631\\u0628\\u064A\\u0644\\u0631 320D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0641\\u0626\\u0629 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"category\",\n              value: formData.category,\n              onChange: handleChange,\n              className: `input-field ${errors.category ? 'border-red-500' : ''}`,\n              children: equipmentCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0645\\u0627\\u0631\\u0643\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"brand\",\n              value: formData.brand,\n              onChange: handleChange,\n              className: \"input-field\",\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: Caterpillar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0645\\u0648\\u062F\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"model\",\n              value: formData.model,\n              onChange: handleChange,\n              className: \"input-field\",\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 320D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u062A\\u0633\\u0644\\u0633\\u0644\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"serialNumber\",\n              value: formData.serialNumber,\n              onChange: handleChange,\n              className: \"input-field\",\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: CAT320D2023001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0633\\u0646\\u0629 \\u0627\\u0644\\u0635\\u0646\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"yearOfManufacture\",\n              value: formData.yearOfManufacture,\n              onChange: handleChange,\n              className: `input-field ${errors.yearOfManufacture ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 2020\",\n              min: \"1900\",\n              max: new Date().getFullYear()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), errors.yearOfManufacture && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.yearOfManufacture\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: \"purchaseDate\",\n              value: formData.purchaseDate,\n              onChange: handleChange,\n              className: \"input-field\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0634\\u0631\\u0627\\u0621 (\\u0631\\u064A\\u0627\\u0644)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"purchasePrice\",\n              value: formData.purchasePrice,\n              onChange: handleChange,\n              className: `input-field ${errors.purchasePrice ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 450000\",\n              min: \"0\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), errors.purchasePrice && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.purchasePrice\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629 (\\u0631\\u064A\\u0627\\u0644)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"currentValue\",\n              value: formData.currentValue,\n              onChange: handleChange,\n              className: `input-field ${errors.currentValue ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 380000\",\n              min: \"0\",\n              step: \"0.01\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), errors.currentValue && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.currentValue\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"location\",\n              value: formData.location,\n              onChange: handleChange,\n              className: \"input-field\",\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: \\u0627\\u0644\\u0631\\u064A\\u0627\\u0636 - \\u0627\\u0644\\u0645\\u0633\\u062A\\u0648\\u062F\\u0639 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"status\",\n              value: formData.status,\n              onChange: handleChange,\n              className: \"input-field\",\n              children: equipmentStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: status.value,\n                children: status.label\n              }, status.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0639\\u0627\\u0645\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"condition\",\n              value: formData.condition,\n              onChange: handleChange,\n              className: \"input-field\",\n              children: equipmentConditions.map(condition => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: condition.value,\n                children: condition.label\n              }, condition.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0633\\u0627\\u0639\\u0627\\u062A \\u0627\\u0644\\u062A\\u0634\\u063A\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"operatingHours\",\n              value: formData.operatingHours,\n              onChange: handleChange,\n              className: `input-field ${errors.operatingHours ? 'border-red-500' : ''}`,\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 2450\",\n              min: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), errors.operatingHours && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.operatingHours\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0641\\u062A\\u0631\\u0629 \\u0627\\u0644\\u0635\\u064A\\u0627\\u0646\\u0629 (\\u0633\\u0627\\u0639\\u0629)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"maintenanceInterval\",\n              value: formData.maintenanceInterval,\n              onChange: handleChange,\n              className: \"input-field\",\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: 250\",\n              min: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"\\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"customerId\",\n              value: formData.customerId,\n              onChange: handleChange,\n              className: \"input-field\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), customersForSelect.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: customer.id,\n                children: [customer.name, \" (\", customer.customerCode, \")\"]\n              }, customer.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"description\",\n            value: formData.description,\n            onChange: handleChange,\n            rows: 3,\n            className: \"input-field\",\n            placeholder: \"\\u0648\\u0635\\u0641 \\u062A\\u0641\\u0635\\u064A\\u0644\\u064A \\u0644\\u0644\\u0645\\u0639\\u062F\\u0629...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-1\",\n            children: \"\\u0627\\u0644\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            name: \"notes\",\n            value: formData.notes,\n            onChange: handleChange,\n            rows: 3,\n            className: \"input-field\",\n            placeholder: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 space-x-reverse pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"btn-secondary\",\n            disabled: loading,\n            children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn-primary\",\n            disabled: loading,\n            children: loading ? 'جاري الحفظ...' : equipment ? 'تحديث' : 'إضافة'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n};\n_s(EquipmentModal, \"HSwHYS0i2hgUHtZGWWZNGjXU2Gs=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = EquipmentModal;\nexport default EquipmentModal;\nvar _c;\n$RefreshReg$(_c, \"EquipmentModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "XMarkIcon", "createEquipment", "updateEquipment", "fetchCustomersForSelect", "selectCustomersForSelect", "selectEquipmentLoading", "equipmentCategories", "equipmentStatuses", "equipmentConditions", "toast", "jsxDEV", "_jsxDEV", "EquipmentModal", "isOpen", "onClose", "equipment", "onSuccess", "_s", "dispatch", "customersForSelect", "loading", "formData", "setFormData", "equipmentNumber", "name", "description", "category", "brand", "model", "serialNumber", "yearOfManufacture", "purchaseDate", "purchasePrice", "currentValue", "location", "status", "condition", "operatingHours", "maintenanceInterval", "notes", "customerId", "errors", "setErrors", "handleChange", "e", "value", "target", "prev", "validateForm", "newErrors", "trim", "Date", "getFullYear", "Object", "keys", "length", "handleSubmit", "preventDefault", "submitData", "parseInt", "parseFloat", "id", "data", "unwrap", "success", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "onChange", "placeholder", "map", "label", "min", "max", "step", "customer", "customerCode", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Fady/frontend/src/components/Equipment/EquipmentModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\nimport {\n  createEquipment,\n  updateEquipment,\n  fetchCustomersForSelect,\n  selectCustomersForSelect,\n  selectEquipmentLoading\n} from '../../store/slices/equipmentSlice';\nimport {\n  equipmentCategories,\n  equipmentStatuses,\n  equipmentConditions\n} from '../../services/equipmentService';\nimport toast from 'react-hot-toast';\n\nconst EquipmentModal = ({ isOpen, onClose, equipment = null, onSuccess }) => {\n  const dispatch = useDispatch();\n  const customersForSelect = useSelector(selectCustomersForSelect);\n  const loading = useSelector(selectEquipmentLoading);\n\n  const [formData, setFormData] = useState({\n    equipmentNumber: '',\n    name: '',\n    description: '',\n    category: 'excavator',\n    brand: '',\n    model: '',\n    serialNumber: '',\n    yearOfManufacture: '',\n    purchaseDate: '',\n    purchasePrice: '',\n    currentValue: '',\n    location: '',\n    status: 'operational',\n    condition: 'good',\n    operatingHours: 0,\n    maintenanceInterval: '',\n    notes: '',\n    customerId: ''\n  });\n\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(fetchCustomersForSelect());\n      \n      if (equipment) {\n        // تعديل معدة موجودة\n        setFormData({\n          equipmentNumber: equipment.equipmentNumber || '',\n          name: equipment.name || '',\n          description: equipment.description || '',\n          category: equipment.category || 'excavator',\n          brand: equipment.brand || '',\n          model: equipment.model || '',\n          serialNumber: equipment.serialNumber || '',\n          yearOfManufacture: equipment.yearOfManufacture || '',\n          purchaseDate: equipment.purchaseDate || '',\n          purchasePrice: equipment.purchasePrice || '',\n          currentValue: equipment.currentValue || '',\n          location: equipment.location || '',\n          status: equipment.status || 'operational',\n          condition: equipment.condition || 'good',\n          operatingHours: equipment.operatingHours || 0,\n          maintenanceInterval: equipment.maintenanceInterval || '',\n          notes: equipment.notes || '',\n          customerId: equipment.customerId || ''\n        });\n      } else {\n        // إضافة معدة جديدة\n        setFormData({\n          equipmentNumber: '',\n          name: '',\n          description: '',\n          category: 'excavator',\n          brand: '',\n          model: '',\n          serialNumber: '',\n          yearOfManufacture: '',\n          purchaseDate: '',\n          purchasePrice: '',\n          currentValue: '',\n          location: '',\n          status: 'operational',\n          condition: 'good',\n          operatingHours: 0,\n          maintenanceInterval: '',\n          notes: '',\n          customerId: ''\n        });\n      }\n      setErrors({});\n    }\n  }, [isOpen, equipment, dispatch]);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // مسح الخطأ عند التعديل\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.equipmentNumber.trim()) {\n      newErrors.equipmentNumber = 'رقم المعدة مطلوب';\n    }\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'اسم المعدة مطلوب';\n    }\n\n    if (!formData.category) {\n      newErrors.category = 'فئة المعدة مطلوبة';\n    }\n\n    if (formData.yearOfManufacture && (formData.yearOfManufacture < 1900 || formData.yearOfManufacture > new Date().getFullYear())) {\n      newErrors.yearOfManufacture = 'سنة الصنع غير صحيحة';\n    }\n\n    if (formData.purchasePrice && formData.purchasePrice < 0) {\n      newErrors.purchasePrice = 'سعر الشراء لا يمكن أن يكون سالباً';\n    }\n\n    if (formData.currentValue && formData.currentValue < 0) {\n      newErrors.currentValue = 'القيمة الحالية لا يمكن أن تكون سالبة';\n    }\n\n    if (formData.operatingHours < 0) {\n      newErrors.operatingHours = 'ساعات التشغيل لا يمكن أن تكون سالبة';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      const submitData = {\n        ...formData,\n        yearOfManufacture: formData.yearOfManufacture ? parseInt(formData.yearOfManufacture) : null,\n        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : null,\n        currentValue: formData.currentValue ? parseFloat(formData.currentValue) : null,\n        operatingHours: parseInt(formData.operatingHours) || 0,\n        maintenanceInterval: formData.maintenanceInterval ? parseInt(formData.maintenanceInterval) : null,\n        customerId: formData.customerId || null\n      };\n\n      if (equipment) {\n        // تحديث معدة موجودة\n        await dispatch(updateEquipment({ id: equipment.id, data: submitData })).unwrap();\n        toast.success('تم تحديث المعدة بنجاح');\n      } else {\n        // إضافة معدة جديدة\n        await dispatch(createEquipment(submitData)).unwrap();\n        toast.success('تم إضافة المعدة بنجاح');\n      }\n\n      onSuccess && onSuccess();\n      onClose();\n    } catch (error) {\n      toast.error(error || 'حدث خطأ أثناء حفظ المعدة');\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            {equipment ? 'تعديل المعدة' : 'إضافة معدة جديدة'}\n          </h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* رقم المعدة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                رقم المعدة *\n              </label>\n              <input\n                type=\"text\"\n                name=\"equipmentNumber\"\n                value={formData.equipmentNumber}\n                onChange={handleChange}\n                className={`input-field ${errors.equipmentNumber ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: CAT001\"\n              />\n              {errors.equipmentNumber && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.equipmentNumber}</p>\n              )}\n            </div>\n\n            {/* اسم المعدة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                اسم المعدة *\n              </label>\n              <input\n                type=\"text\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleChange}\n                className={`input-field ${errors.name ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: حفارة كاتربيلر 320D\"\n              />\n              {errors.name && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.name}</p>\n              )}\n            </div>\n\n            {/* الفئة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الفئة *\n              </label>\n              <select\n                name=\"category\"\n                value={formData.category}\n                onChange={handleChange}\n                className={`input-field ${errors.category ? 'border-red-500' : ''}`}\n              >\n                {equipmentCategories.map(category => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n              {errors.category && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.category}</p>\n              )}\n            </div>\n\n            {/* الماركة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الماركة\n              </label>\n              <input\n                type=\"text\"\n                name=\"brand\"\n                value={formData.brand}\n                onChange={handleChange}\n                className=\"input-field\"\n                placeholder=\"مثال: Caterpillar\"\n              />\n            </div>\n\n            {/* الموديل */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الموديل\n              </label>\n              <input\n                type=\"text\"\n                name=\"model\"\n                value={formData.model}\n                onChange={handleChange}\n                className=\"input-field\"\n                placeholder=\"مثال: 320D\"\n              />\n            </div>\n\n            {/* الرقم التسلسلي */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الرقم التسلسلي\n              </label>\n              <input\n                type=\"text\"\n                name=\"serialNumber\"\n                value={formData.serialNumber}\n                onChange={handleChange}\n                className=\"input-field\"\n                placeholder=\"مثال: CAT320D2023001\"\n              />\n            </div>\n\n            {/* سنة الصنع */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                سنة الصنع\n              </label>\n              <input\n                type=\"number\"\n                name=\"yearOfManufacture\"\n                value={formData.yearOfManufacture}\n                onChange={handleChange}\n                className={`input-field ${errors.yearOfManufacture ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: 2020\"\n                min=\"1900\"\n                max={new Date().getFullYear()}\n              />\n              {errors.yearOfManufacture && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.yearOfManufacture}</p>\n              )}\n            </div>\n\n            {/* تاريخ الشراء */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                تاريخ الشراء\n              </label>\n              <input\n                type=\"date\"\n                name=\"purchaseDate\"\n                value={formData.purchaseDate}\n                onChange={handleChange}\n                className=\"input-field\"\n              />\n            </div>\n\n            {/* سعر الشراء */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                سعر الشراء (ريال)\n              </label>\n              <input\n                type=\"number\"\n                name=\"purchasePrice\"\n                value={formData.purchasePrice}\n                onChange={handleChange}\n                className={`input-field ${errors.purchasePrice ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: 450000\"\n                min=\"0\"\n                step=\"0.01\"\n              />\n              {errors.purchasePrice && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.purchasePrice}</p>\n              )}\n            </div>\n\n            {/* القيمة الحالية */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                القيمة الحالية (ريال)\n              </label>\n              <input\n                type=\"number\"\n                name=\"currentValue\"\n                value={formData.currentValue}\n                onChange={handleChange}\n                className={`input-field ${errors.currentValue ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: 380000\"\n                min=\"0\"\n                step=\"0.01\"\n              />\n              {errors.currentValue && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.currentValue}</p>\n              )}\n            </div>\n\n            {/* الموقع */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الموقع\n              </label>\n              <input\n                type=\"text\"\n                name=\"location\"\n                value={formData.location}\n                onChange={handleChange}\n                className=\"input-field\"\n                placeholder=\"مثال: الرياض - المستودع الرئيسي\"\n              />\n            </div>\n\n            {/* الحالة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الحالة\n              </label>\n              <select\n                name=\"status\"\n                value={formData.status}\n                onChange={handleChange}\n                className=\"input-field\"\n              >\n                {equipmentStatuses.map(status => (\n                  <option key={status.value} value={status.value}>\n                    {status.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* الحالة العامة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                الحالة العامة\n              </label>\n              <select\n                name=\"condition\"\n                value={formData.condition}\n                onChange={handleChange}\n                className=\"input-field\"\n              >\n                {equipmentConditions.map(condition => (\n                  <option key={condition.value} value={condition.value}>\n                    {condition.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* ساعات التشغيل */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                ساعات التشغيل\n              </label>\n              <input\n                type=\"number\"\n                name=\"operatingHours\"\n                value={formData.operatingHours}\n                onChange={handleChange}\n                className={`input-field ${errors.operatingHours ? 'border-red-500' : ''}`}\n                placeholder=\"مثال: 2450\"\n                min=\"0\"\n              />\n              {errors.operatingHours && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.operatingHours}</p>\n              )}\n            </div>\n\n            {/* فترة الصيانة */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                فترة الصيانة (ساعة)\n              </label>\n              <input\n                type=\"number\"\n                name=\"maintenanceInterval\"\n                value={formData.maintenanceInterval}\n                onChange={handleChange}\n                className=\"input-field\"\n                placeholder=\"مثال: 250\"\n                min=\"1\"\n              />\n            </div>\n\n            {/* العميل */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                العميل\n              </label>\n              <select\n                name=\"customerId\"\n                value={formData.customerId}\n                onChange={handleChange}\n                className=\"input-field\"\n              >\n                <option value=\"\">اختر العميل</option>\n                {customersForSelect.map(customer => (\n                  <option key={customer.id} value={customer.id}>\n                    {customer.name} ({customer.customerCode})\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {/* الوصف */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              الوصف\n            </label>\n            <textarea\n              name=\"description\"\n              value={formData.description}\n              onChange={handleChange}\n              rows={3}\n              className=\"input-field\"\n              placeholder=\"وصف تفصيلي للمعدة...\"\n            />\n          </div>\n\n          {/* الملاحظات */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              الملاحظات\n            </label>\n            <textarea\n              name=\"notes\"\n              value={formData.notes}\n              onChange={handleChange}\n              rows={3}\n              className=\"input-field\"\n              placeholder=\"ملاحظات إضافية...\"\n            />\n          </div>\n\n          {/* أزرار الحفظ والإلغاء */}\n          <div className=\"flex justify-end space-x-3 space-x-reverse pt-4\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"btn-secondary\"\n              disabled={loading}\n            >\n              إلغاء\n            </button>\n            <button\n              type=\"submit\"\n              className=\"btn-primary\"\n              disabled={loading}\n            >\n              {loading ? 'جاري الحفظ...' : (equipment ? 'تحديث' : 'إضافة')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default EquipmentModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SACEC,eAAe,EACfC,eAAe,EACfC,uBAAuB,EACvBC,wBAAwB,EACxBC,sBAAsB,QACjB,mCAAmC;AAC1C,SACEC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,QACd,iCAAiC;AACxC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,SAAS,GAAG,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,kBAAkB,GAAGpB,WAAW,CAACK,wBAAwB,CAAC;EAChE,MAAMgB,OAAO,GAAGrB,WAAW,CAACM,sBAAsB,CAAC;EAEnD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACvC2B,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,YAAY,EAAE,EAAE;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,aAAa;IACrBC,SAAS,EAAE,MAAM;IACjBC,cAAc,EAAE,CAAC;IACjBC,mBAAmB,EAAE,EAAE;IACvBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,IAAIgB,MAAM,EAAE;MACVK,QAAQ,CAACf,uBAAuB,CAAC,CAAC,CAAC;MAEnC,IAAIY,SAAS,EAAE;QACb;QACAO,WAAW,CAAC;UACVC,eAAe,EAAER,SAAS,CAACQ,eAAe,IAAI,EAAE;UAChDC,IAAI,EAAET,SAAS,CAACS,IAAI,IAAI,EAAE;UAC1BC,WAAW,EAAEV,SAAS,CAACU,WAAW,IAAI,EAAE;UACxCC,QAAQ,EAAEX,SAAS,CAACW,QAAQ,IAAI,WAAW;UAC3CC,KAAK,EAAEZ,SAAS,CAACY,KAAK,IAAI,EAAE;UAC5BC,KAAK,EAAEb,SAAS,CAACa,KAAK,IAAI,EAAE;UAC5BC,YAAY,EAAEd,SAAS,CAACc,YAAY,IAAI,EAAE;UAC1CC,iBAAiB,EAAEf,SAAS,CAACe,iBAAiB,IAAI,EAAE;UACpDC,YAAY,EAAEhB,SAAS,CAACgB,YAAY,IAAI,EAAE;UAC1CC,aAAa,EAAEjB,SAAS,CAACiB,aAAa,IAAI,EAAE;UAC5CC,YAAY,EAAElB,SAAS,CAACkB,YAAY,IAAI,EAAE;UAC1CC,QAAQ,EAAEnB,SAAS,CAACmB,QAAQ,IAAI,EAAE;UAClCC,MAAM,EAAEpB,SAAS,CAACoB,MAAM,IAAI,aAAa;UACzCC,SAAS,EAAErB,SAAS,CAACqB,SAAS,IAAI,MAAM;UACxCC,cAAc,EAAEtB,SAAS,CAACsB,cAAc,IAAI,CAAC;UAC7CC,mBAAmB,EAAEvB,SAAS,CAACuB,mBAAmB,IAAI,EAAE;UACxDC,KAAK,EAAExB,SAAS,CAACwB,KAAK,IAAI,EAAE;UAC5BC,UAAU,EAAEzB,SAAS,CAACyB,UAAU,IAAI;QACtC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAlB,WAAW,CAAC;UACVC,eAAe,EAAE,EAAE;UACnBC,IAAI,EAAE,EAAE;UACRC,WAAW,EAAE,EAAE;UACfC,QAAQ,EAAE,WAAW;UACrBC,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTC,YAAY,EAAE,EAAE;UAChBC,iBAAiB,EAAE,EAAE;UACrBC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,EAAE;UAChBC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAE,aAAa;UACrBC,SAAS,EAAE,MAAM;UACjBC,cAAc,EAAE,CAAC;UACjBC,mBAAmB,EAAE,EAAE;UACvBC,KAAK,EAAE,EAAE;UACTC,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;MACAE,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAAC7B,MAAM,EAAEE,SAAS,EAAEG,QAAQ,CAAC,CAAC;EAEjC,MAAMyB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEpB,IAAI;MAAEqB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCxB,WAAW,CAACyB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACvB,IAAI,GAAGqB;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIJ,MAAM,CAACjB,IAAI,CAAC,EAAE;MAChBkB,SAAS,CAACK,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACvB,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMwB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC5B,QAAQ,CAACE,eAAe,CAAC2B,IAAI,CAAC,CAAC,EAAE;MACpCD,SAAS,CAAC1B,eAAe,GAAG,kBAAkB;IAChD;IAEA,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACzB,IAAI,GAAG,kBAAkB;IACrC;IAEA,IAAI,CAACH,QAAQ,CAACK,QAAQ,EAAE;MACtBuB,SAAS,CAACvB,QAAQ,GAAG,mBAAmB;IAC1C;IAEA,IAAIL,QAAQ,CAACS,iBAAiB,KAAKT,QAAQ,CAACS,iBAAiB,GAAG,IAAI,IAAIT,QAAQ,CAACS,iBAAiB,GAAG,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAE;MAC9HH,SAAS,CAACnB,iBAAiB,GAAG,qBAAqB;IACrD;IAEA,IAAIT,QAAQ,CAACW,aAAa,IAAIX,QAAQ,CAACW,aAAa,GAAG,CAAC,EAAE;MACxDiB,SAAS,CAACjB,aAAa,GAAG,mCAAmC;IAC/D;IAEA,IAAIX,QAAQ,CAACY,YAAY,IAAIZ,QAAQ,CAACY,YAAY,GAAG,CAAC,EAAE;MACtDgB,SAAS,CAAChB,YAAY,GAAG,sCAAsC;IACjE;IAEA,IAAIZ,QAAQ,CAACgB,cAAc,GAAG,CAAC,EAAE;MAC/BY,SAAS,CAACZ,cAAc,GAAG,qCAAqC;IAClE;IAEAK,SAAS,CAACO,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACM,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACT,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMU,UAAU,GAAG;QACjB,GAAGrC,QAAQ;QACXS,iBAAiB,EAAET,QAAQ,CAACS,iBAAiB,GAAG6B,QAAQ,CAACtC,QAAQ,CAACS,iBAAiB,CAAC,GAAG,IAAI;QAC3FE,aAAa,EAAEX,QAAQ,CAACW,aAAa,GAAG4B,UAAU,CAACvC,QAAQ,CAACW,aAAa,CAAC,GAAG,IAAI;QACjFC,YAAY,EAAEZ,QAAQ,CAACY,YAAY,GAAG2B,UAAU,CAACvC,QAAQ,CAACY,YAAY,CAAC,GAAG,IAAI;QAC9EI,cAAc,EAAEsB,QAAQ,CAACtC,QAAQ,CAACgB,cAAc,CAAC,IAAI,CAAC;QACtDC,mBAAmB,EAAEjB,QAAQ,CAACiB,mBAAmB,GAAGqB,QAAQ,CAACtC,QAAQ,CAACiB,mBAAmB,CAAC,GAAG,IAAI;QACjGE,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI;MACrC,CAAC;MAED,IAAIzB,SAAS,EAAE;QACb;QACA,MAAMG,QAAQ,CAAChB,eAAe,CAAC;UAAE2D,EAAE,EAAE9C,SAAS,CAAC8C,EAAE;UAAEC,IAAI,EAAEJ;QAAW,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC;QAChFtD,KAAK,CAACuD,OAAO,CAAC,uBAAuB,CAAC;MACxC,CAAC,MAAM;QACL;QACA,MAAM9C,QAAQ,CAACjB,eAAe,CAACyD,UAAU,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC;QACpDtD,KAAK,CAACuD,OAAO,CAAC,uBAAuB,CAAC;MACxC;MAEAhD,SAAS,IAAIA,SAAS,CAAC,CAAC;MACxBF,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdxD,KAAK,CAACwD,KAAK,CAACA,KAAK,IAAI,0BAA0B,CAAC;IAClD;EACF,CAAC;EAED,IAAI,CAACpD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKuD,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFxD,OAAA;MAAKuD,SAAS,EAAC,4FAA4F;MAAAC,QAAA,gBACzGxD,OAAA;QAAKuD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxD,OAAA;UAAIuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC9CpD,SAAS,GAAG,cAAc,GAAG;QAAkB;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL5D,OAAA;UACE6D,OAAO,EAAE1D,OAAQ;UACjBoD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CxD,OAAA,CAACX,SAAS;YAACkE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5D,OAAA;QAAM8D,QAAQ,EAAEjB,YAAa;QAACU,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDxD,OAAA;UAAKuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpDxD,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,iBAAiB;cACtBqB,KAAK,EAAExB,QAAQ,CAACE,eAAgB;cAChCoD,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAAClB,eAAe,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC3EqD,WAAW,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EACD9B,MAAM,CAAClB,eAAe,iBACrBZ,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAAClB;YAAe;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,MAAM;cACXqB,KAAK,EAAExB,QAAQ,CAACG,IAAK;cACrBmD,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACjB,IAAI,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAChEoD,WAAW,EAAC;YAA2B;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,EACD9B,MAAM,CAACjB,IAAI,iBACVb,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACjB;YAAI;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,UAAU;cACfqB,KAAK,EAAExB,QAAQ,CAACK,QAAS;cACzBiD,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACf,QAAQ,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAAAyC,QAAA,EAEnE7D,mBAAmB,CAACuE,GAAG,CAACnD,QAAQ,iBAC/Bf,OAAA;gBAA6BkC,KAAK,EAAEnB,QAAQ,CAACmB,KAAM;gBAAAsB,QAAA,EAChDzC,QAAQ,CAACoD;cAAK,GADJpD,QAAQ,CAACmB,KAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR9B,MAAM,CAACf,QAAQ,iBACdf,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACf;YAAQ;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC9D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,OAAO;cACZqB,KAAK,EAAExB,QAAQ,CAACM,KAAM;cACtBgD,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cACvBU,WAAW,EAAC;YAAmB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,OAAO;cACZqB,KAAK,EAAExB,QAAQ,CAACO,KAAM;cACtB+C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cACvBU,WAAW,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,cAAc;cACnBqB,KAAK,EAAExB,QAAQ,CAACQ,YAAa;cAC7B8C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cACvBU,WAAW,EAAC;YAAsB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACblD,IAAI,EAAC,mBAAmB;cACxBqB,KAAK,EAAExB,QAAQ,CAACS,iBAAkB;cAClC6C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACX,iBAAiB,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC7E8C,WAAW,EAAC,gCAAY;cACxBG,GAAG,EAAC,MAAM;cACVC,GAAG,EAAE,IAAI7B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YAAE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACD9B,MAAM,CAACX,iBAAiB,iBACvBnB,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACX;YAAiB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,cAAc;cACnBqB,KAAK,EAAExB,QAAQ,CAACU,YAAa;cAC7B4C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACblD,IAAI,EAAC,eAAe;cACpBqB,KAAK,EAAExB,QAAQ,CAACW,aAAc;cAC9B2C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACT,aAAa,GAAG,gBAAgB,GAAG,EAAE,EAAG;cACzE4C,WAAW,EAAC,kCAAc;cAC1BG,GAAG,EAAC,GAAG;cACPE,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EACD9B,MAAM,CAACT,aAAa,iBACnBrB,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACT;YAAa;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACblD,IAAI,EAAC,cAAc;cACnBqB,KAAK,EAAExB,QAAQ,CAACY,YAAa;cAC7B0C,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACR,YAAY,GAAG,gBAAgB,GAAG,EAAE,EAAG;cACxE2C,WAAW,EAAC,kCAAc;cAC1BG,GAAG,EAAC,GAAG;cACPE,IAAI,EAAC;YAAM;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EACD9B,MAAM,CAACR,YAAY,iBAClBtB,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACR;YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,MAAM;cACXlD,IAAI,EAAC,UAAU;cACfqB,KAAK,EAAExB,QAAQ,CAACa,QAAS;cACzByC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cACvBU,WAAW,EAAC;YAAiC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,QAAQ;cACbqB,KAAK,EAAExB,QAAQ,CAACc,MAAO;cACvBwC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAEtB5D,iBAAiB,CAACsE,GAAG,CAAC1C,MAAM,iBAC3BxB,OAAA;gBAA2BkC,KAAK,EAAEV,MAAM,CAACU,KAAM;gBAAAsB,QAAA,EAC5ChC,MAAM,CAAC2C;cAAK,GADF3C,MAAM,CAACU,KAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,WAAW;cAChBqB,KAAK,EAAExB,QAAQ,CAACe,SAAU;cAC1BuC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAEtB3D,mBAAmB,CAACqE,GAAG,CAACzC,SAAS,iBAChCzB,OAAA;gBAA8BkC,KAAK,EAAET,SAAS,CAACS,KAAM;gBAAAsB,QAAA,EAClD/B,SAAS,CAAC0C;cAAK,GADL1C,SAAS,CAACS,KAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACblD,IAAI,EAAC,gBAAgB;cACrBqB,KAAK,EAAExB,QAAQ,CAACgB,cAAe;cAC/BsC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAE,eAAezB,MAAM,CAACJ,cAAc,GAAG,gBAAgB,GAAG,EAAE,EAAG;cAC1EuC,WAAW,EAAC,gCAAY;cACxBG,GAAG,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EACD9B,MAAM,CAACJ,cAAc,iBACpB1B,OAAA;cAAGuD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1B,MAAM,CAACJ;YAAc;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACpE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACblD,IAAI,EAAC,qBAAqB;cAC1BqB,KAAK,EAAExB,QAAQ,CAACiB,mBAAoB;cACpCqC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cACvBU,WAAW,EAAC,+BAAW;cACvBG,GAAG,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN5D,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5D,OAAA;cACEa,IAAI,EAAC,YAAY;cACjBqB,KAAK,EAAExB,QAAQ,CAACmB,UAAW;cAC3BmC,QAAQ,EAAEhC,YAAa;cACvBuB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvBxD,OAAA;gBAAQkC,KAAK,EAAC,EAAE;gBAAAsB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACpCpD,kBAAkB,CAAC0D,GAAG,CAACK,QAAQ,iBAC9BvE,OAAA;gBAA0BkC,KAAK,EAAEqC,QAAQ,CAACrB,EAAG;gBAAAM,QAAA,GAC1Ce,QAAQ,CAAC1D,IAAI,EAAC,IAAE,EAAC0D,QAAQ,CAACC,YAAY,EAAC,GAC1C;cAAA,GAFaD,QAAQ,CAACrB,EAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAOuD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEa,IAAI,EAAC,aAAa;YAClBqB,KAAK,EAAExB,QAAQ,CAACI,WAAY;YAC5BkD,QAAQ,EAAEhC,YAAa;YACvByC,IAAI,EAAE,CAAE;YACRlB,SAAS,EAAC,aAAa;YACvBU,WAAW,EAAC;UAAsB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAOuD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5D,OAAA;YACEa,IAAI,EAAC,OAAO;YACZqB,KAAK,EAAExB,QAAQ,CAACkB,KAAM;YACtBoC,QAAQ,EAAEhC,YAAa;YACvByC,IAAI,EAAE,CAAE;YACRlB,SAAS,EAAC,aAAa;YACvBU,WAAW,EAAC;UAAmB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5D,OAAA;UAAKuD,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DxD,OAAA;YACE+D,IAAI,EAAC,QAAQ;YACbF,OAAO,EAAE1D,OAAQ;YACjBoD,SAAS,EAAC,eAAe;YACzBmB,QAAQ,EAAEjE,OAAQ;YAAA+C,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA;YACE+D,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,aAAa;YACvBmB,QAAQ,EAAEjE,OAAQ;YAAA+C,QAAA,EAEjB/C,OAAO,GAAG,eAAe,GAAIL,SAAS,GAAG,OAAO,GAAG;UAAQ;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA5gBIL,cAAc;EAAA,QACDd,WAAW,EACDC,WAAW,EACtBA,WAAW;AAAA;AAAAuF,EAAA,GAHvB1E,cAAc;AA8gBpB,eAAeA,cAAc;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
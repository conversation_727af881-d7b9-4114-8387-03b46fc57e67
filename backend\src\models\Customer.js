const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Customer = sequelize.define('Customer', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    customerCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'customer_code',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [2, 200],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM,
      values: ['individual', 'company'],
      defaultValue: 'company',
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    taxNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'tax_number',
      validate: {
        len: [5, 50]
      }
    },
    // Billing Address
    billingAddress: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'billing_address'
    },
    billingCity: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'billing_city',
      validate: {
        len: [2, 100]
      }
    },
    billingCountry: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'billing_country',
      validate: {
        len: [2, 100]
      }
    },
    // Contact Person
    contactPersonName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_name',
      validate: {
        len: [2, 100]
      }
    },
    contactPersonPhone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'contact_person_phone',
      validate: {
        len: [10, 20]
      }
    },
    // Financial Information
    creditLimit: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'credit_limit',
      validate: {
        min: 0
      }
    },
    currentBalance: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'current_balance'
    },
    // Status and Notes
    status: {
      type: DataTypes.ENUM,
      values: ['active', 'inactive', 'blocked'],
      defaultValue: 'active',
      allowNull: false
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'customers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['customer_code']
      },
      {
        fields: ['name']
      },
      {
        fields: ['status']
      }
    ]
  });

  // Instance methods
  Customer.prototype.getFullAddress = function() {
    const address = this.billingAddress;
    const city = this.billingCity;
    const country = this.billingCountry;

    const parts = [address, city, country].filter(Boolean);
    return parts.join(', ');
  };

  Customer.prototype.isOverCreditLimit = function() {
    return parseFloat(this.currentBalance) > parseFloat(this.creditLimit);
  };

  Customer.prototype.getAvailableCredit = function() {
    const available = parseFloat(this.creditLimit) - parseFloat(this.currentBalance);
    return Math.max(0, available);
  };

  // Associations
  Customer.associate = function(models) {
    // Customer has many Equipment
    if (models.Equipment) {
      Customer.hasMany(models.Equipment, {
        foreignKey: 'customer_id',
        as: 'equipment'
      });
    }
  };

  return Customer;
};

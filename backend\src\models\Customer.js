const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Customer = sequelize.define('Customer', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    customerCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'customer_code',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [2, 200],
        notEmpty: true
      }
    },
    type: {
      type: DataTypes.ENUM,
      values: ['individual', 'company'],
      defaultValue: 'company',
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    mobile: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    fax: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20]
      }
    },
    website: {
      type: DataTypes.STRING(200),
      allowNull: true,
      validate: {
        isUrl: true,
        len: [5, 200]
      }
    },
    taxNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'tax_number',
      validate: {
        len: [5, 50]
      }
    },
    registrationNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'registration_number',
      validate: {
        len: [5, 50]
      }
    },
    // Billing Address
    billingAddress: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'billing_address'
    },
    billingCity: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'billing_city',
      validate: {
        len: [2, 100]
      }
    },
    billingState: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'billing_state',
      validate: {
        len: [2, 100]
      }
    },
    billingCountry: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'billing_country',
      validate: {
        len: [2, 100]
      }
    },
    billingPostalCode: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'billing_postal_code',
      validate: {
        len: [3, 20]
      }
    },
    // Shipping Address
    shippingAddress: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'shipping_address'
    },
    shippingCity: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'shipping_city',
      validate: {
        len: [2, 100]
      }
    },
    shippingState: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'shipping_state',
      validate: {
        len: [2, 100]
      }
    },
    shippingCountry: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'shipping_country',
      validate: {
        len: [2, 100]
      }
    },
    shippingPostalCode: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'shipping_postal_code',
      validate: {
        len: [3, 20]
      }
    },
    // Contact Person
    contactPersonName: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_name',
      validate: {
        len: [2, 100]
      }
    },
    contactPersonTitle: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_title',
      validate: {
        len: [2, 100]
      }
    },
    contactPersonEmail: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'contact_person_email',
      validate: {
        isEmail: true,
        len: [5, 100]
      }
    },
    contactPersonPhone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'contact_person_phone',
      validate: {
        len: [10, 20]
      }
    },
    // Financial Information
    creditLimit: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'credit_limit',
      validate: {
        min: 0
      }
    },
    currentBalance: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'current_balance'
    },
    paymentTerms: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'payment_terms',
      validate: {
        len: [2, 100]
      }
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD',
      validate: {
        len: [3, 3],
        isUppercase: true
      }
    },
    // Status and Notes
    status: {
      type: DataTypes.ENUM,
      values: ['active', 'inactive', 'blocked'],
      defaultValue: 'active',
      allowNull: false
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    internalNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'internal_notes'
    }
  }, {
    tableName: 'customers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['customer_code']
      },
      {
        fields: ['name']
      },
      {
        fields: ['email']
      },
      {
        fields: ['status']
      },
      {
        fields: ['type']
      }
    ]
  });

  // Instance methods
  Customer.prototype.getFullAddress = function(type = 'billing') {
    const prefix = type === 'shipping' ? 'shipping' : 'billing';
    const address = this[`${prefix}Address`];
    const city = this[`${prefix}City`];
    const state = this[`${prefix}State`];
    const country = this[`${prefix}Country`];
    const postalCode = this[`${prefix}PostalCode`];
    
    const parts = [address, city, state, country, postalCode].filter(Boolean);
    return parts.join(', ');
  };

  Customer.prototype.isOverCreditLimit = function() {
    return parseFloat(this.currentBalance) > parseFloat(this.creditLimit);
  };

  Customer.prototype.getAvailableCredit = function() {
    const available = parseFloat(this.creditLimit) - parseFloat(this.currentBalance);
    return Math.max(0, available);
  };

  // Associations
  Customer.associate = function(models) {
    // Customer has many Invoices
    Customer.hasMany(models.Invoice, {
      foreignKey: 'customer_id',
      as: 'invoices'
    });

    // Customer has many WorkOrders
    Customer.hasMany(models.WorkOrder, {
      foreignKey: 'customer_id',
      as: 'workOrders'
    });

    // Customer has many Equipment
    Customer.hasMany(models.Equipment, {
      foreignKey: 'customer_id',
      as: 'equipment'
    });
  };

  return Customer;
};

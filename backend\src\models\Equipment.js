const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Equipment = sequelize.define('Equipment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    equipmentNumber: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'equipment_number',
      validate: {
        len: [3, 50],
        notEmpty: true
      }
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        len: [2, 50],
        notEmpty: true,
        isIn: [['excavator', 'bulldozer', 'crane', 'loader', 'truck', 'generator', 'compressor', 'other']]
      }
    },
    brand: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [2, 50]
      }
    },
    model: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [2, 50]
      }
    },
    serialNumber: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true,
      field: 'serial_number',
      validate: {
        len: [3, 100]
      }
    },
    yearOfManufacture: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'year_of_manufacture',
      validate: {
        min: 1900,
        max: new Date().getFullYear() + 1
      }
    },
    purchaseDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'purchase_date'
    },
    purchasePrice: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'purchase_price',
      validate: {
        min: 0
      }
    },
    currentValue: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      field: 'current_value',
      validate: {
        min: 0
      }
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [2, 100]
      }
    },
    status: {
      type: DataTypes.ENUM,
      values: ['operational', 'maintenance', 'repair', 'out_of_service', 'sold'],
      defaultValue: 'operational',
      allowNull: false
    },
    condition: {
      type: DataTypes.ENUM,
      values: ['excellent', 'good', 'fair', 'poor'],
      defaultValue: 'good',
      allowNull: false
    },
    operatingHours: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'operating_hours',
      validate: {
        min: 0
      }
    },
    lastMaintenanceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'last_maintenance_date'
    },
    nextMaintenanceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'next_maintenance_date'
    },
    maintenanceInterval: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'maintenance_interval',
      comment: 'Maintenance interval in hours',
      validate: {
        min: 1
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    images: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: []
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'equipment',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['equipment_number']
      },
      {
        fields: ['category']
      },
      {
        fields: ['status']
      },
      {
        fields: ['serial_number']
      }
    ]
  });

  // Instance methods
  Equipment.prototype.isMaintenanceDue = function() {
    if (!this.nextMaintenanceDate) return false;
    return new Date() >= new Date(this.nextMaintenanceDate);
  };

  Equipment.prototype.getDaysUntilMaintenance = function() {
    if (!this.nextMaintenanceDate) return null;
    const today = new Date();
    const maintenanceDate = new Date(this.nextMaintenanceDate);
    const diffTime = maintenanceDate - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Associations
  Equipment.associate = function(models) {
    // Equipment has many WorkOrders
    if (models.WorkOrder) {
      Equipment.hasMany(models.WorkOrder, {
        foreignKey: 'equipment_id',
        as: 'workOrders'
      });
    }

    // Equipment has many EquipmentParts
    if (models.EquipmentPart) {
      Equipment.hasMany(models.EquipmentPart, {
        foreignKey: 'equipment_id',
        as: 'parts'
      });
    }

    // Equipment belongs to Customer
    if (models.Customer) {
      Equipment.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer'
      });
    }
  };

  return Equipment;
};

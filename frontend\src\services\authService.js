import api from './api';

const authService = {
  // تسجيل الدخول
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  // التسجيل
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  // تسجيل الخروج
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  // الحصول على الملف الشخصي
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  // تحديث الملف الشخصي
  updateProfile: async (userData) => {
    const response = await api.put('/auth/profile', userData);
    return response.data;
  },

  // تغيير كلمة المرور
  changePassword: async (passwordData) => {
    const response = await api.put('/auth/change-password', passwordData);
    return response.data;
  },

  // التحقق من صحة التوكن
  verifyToken: async () => {
    const response = await api.get('/auth/verify');
    return response.data;
  },
};

export default authService;

const { WorkOrder, Customer, Equipment, WorkOrderPart, WorkLog, InventoryItem } = require('../models');
const { Op } = require('sequelize');

// الحصول على جميع أوامر الصيانة
const getAllWorkOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      priority = '',
      technician = '',
      dateFrom = '',
      dateTo = '',
      sortBy = 'received_date',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء شروط البحث
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { workOrderNumber: { [Op.iLike]: `%${search}%` } },
        { problemDescription: { [Op.iLike]: `%${search}%` } },
        { assignedTechnician: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    if (status) {
      whereClause.status = status;
    }
    
    if (priority) {
      whereClause.priority = priority;
    }

    if (technician) {
      whereClause.assignedTechnician = { [Op.iLike]: `%${technician}%` };
    }

    if (dateFrom && dateTo) {
      whereClause.receivedDate = {
        [Op.between]: [dateFrom, dateTo]
      };
    } else if (dateFrom) {
      whereClause.receivedDate = {
        [Op.gte]: dateFrom
      };
    } else if (dateTo) {
      whereClause.receivedDate = {
        [Op.lte]: dateTo
      };
    }

    const { count, rows: workOrders } = await WorkOrder.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customerCode', 'phone']
        },
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id', 'name', 'equipmentNumber', 'brand', 'model']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['receivedDate', sortOrder.toUpperCase()]],
      distinct: true
    });

    res.json({
      success: true,
      data: {
        workOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Get all work orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch work orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على أمر صيانة واحد
const getWorkOrderById = async (req, res) => {
  try {
    const { id } = req.params;

    const workOrder = await WorkOrder.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customerCode', 'phone', 'email']
        },
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id', 'name', 'equipmentNumber', 'brand', 'model', 'serialNumber']
        },
        // {
        //   model: WorkOrderPart,
        //   as: 'parts',
        //   attributes: ['id', 'quantityUsed', 'unitCost', 'totalCost', 'notes']
        // },
        // {
        //   model: WorkLog,
        //   as: 'workLogs',
        //   attributes: ['id', 'logDate', 'technicianName', 'hoursWorked', 'workDescription', 'notes'],
        //   order: [['logDate', 'DESC']]
        // }
      ]
    });

    if (!workOrder) {
      return res.status(404).json({
        success: false,
        message: 'Work order not found'
      });
    }

    res.json({
      success: true,
      data: { workOrder }
    });

  } catch (error) {
    console.error('Get work order by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch work order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// إنشاء أمر صيانة جديد
const createWorkOrder = async (req, res) => {
  try {
    const {
      workOrderNumber,
      customerId,
      equipmentId,
      receivedBy,
      problemDescription,
      customerComplaint,
      priority = 'medium',
      estimatedCost,
      estimatedCompletionDate,
      assignedTechnician
    } = req.body;

    // التحقق من عدم تكرار رقم أمر العمل
    const existingWorkOrder = await WorkOrder.findOne({
      where: { workOrderNumber }
    });

    if (existingWorkOrder) {
      return res.status(400).json({
        success: false,
        message: 'Work order number already exists'
      });
    }

    // التحقق من وجود العميل والمعدة
    const customer = await Customer.findByPk(customerId);
    if (!customer) {
      return res.status(400).json({
        success: false,
        message: 'Customer not found'
      });
    }

    const equipment = await Equipment.findByPk(equipmentId);
    if (!equipment) {
      return res.status(400).json({
        success: false,
        message: 'Equipment not found'
      });
    }

    // إنشاء أمر الصيانة
    const workOrder = await WorkOrder.create({
      workOrderNumber,
      customerId,
      equipmentId,
      receivedDate: new Date(),
      receivedBy,
      problemDescription,
      customerComplaint,
      priority,
      estimatedCost,
      estimatedCompletionDate,
      assignedTechnician,
      status: 'received'
    });

    // تحديث حالة المعدة
    await equipment.update({
      status: 'in_workshop'
    });

    // إعادة جلب أمر العمل مع العلاقات
    const workOrderWithRelations = await WorkOrder.findByPk(workOrder.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customerCode', 'phone']
        },
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id', 'name', 'equipmentNumber', 'brand', 'model']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Work order created successfully',
      data: { workOrder: workOrderWithRelations }
    });

  } catch (error) {
    console.error('Create work order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create work order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// تحديث أمر صيانة
const updateWorkOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const workOrder = await WorkOrder.findByPk(id);
    if (!workOrder) {
      return res.status(404).json({
        success: false,
        message: 'Work order not found'
      });
    }

    // التحقق من عدم تكرار رقم أمر العمل
    if (updateData.workOrderNumber && updateData.workOrderNumber !== workOrder.workOrderNumber) {
      const existingWorkOrder = await WorkOrder.findOne({
        where: { 
          workOrderNumber: updateData.workOrderNumber,
          id: { [Op.ne]: id }
        }
      });

      if (existingWorkOrder) {
        return res.status(400).json({
          success: false,
          message: 'Work order number already exists'
        });
      }
    }

    // تحديث التواريخ حسب الحالة
    if (updateData.status) {
      switch (updateData.status) {
        case 'in_progress':
          if (!workOrder.startedDate) {
            updateData.startedDate = new Date();
          }
          break;
        case 'completed':
          updateData.completedDate = new Date();
          break;
        case 'delivered':
          updateData.deliveredDate = new Date();
          // تحديث حالة المعدة
          const equipment = await Equipment.findByPk(workOrder.equipmentId);
          if (equipment) {
            await equipment.update({
              status: 'operational'
            });
          }
          break;
      }
    }

    // تحديث أمر العمل
    await workOrder.update(updateData);

    // إعادة جلب أمر العمل مع العلاقات
    const updatedWorkOrder = await WorkOrder.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'customerCode', 'phone']
        },
        {
          model: Equipment,
          as: 'equipment',
          attributes: ['id', 'name', 'equipmentNumber', 'brand', 'model']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Work order updated successfully',
      data: { workOrder: updatedWorkOrder }
    });

  } catch (error) {
    console.error('Update work order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update work order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// حذف أمر صيانة
const deleteWorkOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const workOrder = await WorkOrder.findByPk(id);
    if (!workOrder) {
      return res.status(404).json({
        success: false,
        message: 'Work order not found'
      });
    }

    // إعادة تعيين حالة المعدة
    const equipment = await Equipment.findByPk(workOrder.equipmentId);
    if (equipment) {
      await equipment.update({
        status: 'operational'
      });
    }

    await workOrder.destroy();

    res.json({
      success: true,
      message: 'Work order deleted successfully'
    });

  } catch (error) {
    console.error('Delete work order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete work order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// الحصول على إحصائيات أوامر الصيانة
const getWorkOrderStats = async (req, res) => {
  try {
    const totalWorkOrders = await WorkOrder.count();

    // إحصائيات الحالة
    const statusStatsRaw = await WorkOrder.sequelize.query(
      'SELECT status, COUNT(*) as count FROM work_orders GROUP BY status',
      { type: WorkOrder.sequelize.QueryTypes.SELECT }
    );

    // إحصائيات الأولوية
    const priorityStatsRaw = await WorkOrder.sequelize.query(
      'SELECT priority, COUNT(*) as count FROM work_orders GROUP BY priority',
      { type: WorkOrder.sequelize.QueryTypes.SELECT }
    );

    // أوامر العمل المتأخرة
    const overdueWorkOrders = await WorkOrder.count({
      where: {
        estimatedCompletionDate: {
          [Op.lt]: new Date()
        },
        status: {
          [Op.notIn]: ['completed', 'delivered', 'cancelled']
        }
      }
    });

    // المعدات في الورشة حالياً
    const equipmentInWorkshop = await Equipment.count({
      where: {
        status: 'in_workshop'
      }
    });

    // إيرادات الشهر الحالي
    const currentMonth = new Date();
    const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

    const monthlyRevenue = await WorkOrder.sum('totalCost', {
      where: {
        status: 'delivered',
        deliveredDate: {
          [Op.between]: [startOfMonth, endOfMonth]
        }
      }
    });

    // تحويل الإحصائيات إلى كائنات
    const statusStats = statusStatsRaw.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {});

    const priorityStats = priorityStatsRaw.reduce((acc, item) => {
      acc[item.priority] = parseInt(item.count);
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        totalWorkOrders,
        overdueWorkOrders,
        equipmentInWorkshop,
        monthlyRevenue: monthlyRevenue || 0,
        statusStats,
        priorityStats
      }
    });

  } catch (error) {
    console.error('Get work order stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch work order statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAllWorkOrders,
  getWorkOrderById,
  createWorkOrder,
  updateWorkOrder,
  deleteWorkOrder,
  getWorkOrderStats
};
